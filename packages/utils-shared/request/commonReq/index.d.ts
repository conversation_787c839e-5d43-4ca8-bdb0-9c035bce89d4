import Taro from '@tarojs/taro';
export interface RequestConf extends Taro.request.Option {
    timeout?: number; // 超时时间
    isTabPage?: boolean;
    isPrefetch?: boolean;
    contentType?: string;
    isNeedLogin?: boolean;
    isFirstScreen?: boolean;
    errorToast?: boolean; // 统计拦截错误toast，默认是true
}

interface CommonResponseWithErrno<T> {
    data: T;
    lid: string;
    msg: string;
    toast: string;
    errno: number;
    status?: string;
    applid?: string;
    responseHeader: {
        [key: string]: string;
    };
}
interface CommonResponseWithStatus<T> {
    data: T;
    lid: string;
    msg: string;
    toast: string;
    status: number;
    applid?: string;
    responseHeader: {
        [key: string]: string;
    };
}
export type CommonResponseVal<T> = CommonResponseWithStatus<T> | CommonResponseWithErrno<T>;

export interface AfterRequestReturnVal<T, U> {
    spentTime: number; // ms
    conf: RequestConf;
    response: CommonResponseVal<T | U> | null;
    fullResponse: unknown;
}

export interface UnLoginReturnVal {
    conf: RequestConf;
    response: CommonResponseVal<unknown>;
    fullResponse: unknown;
}

export interface RejectErrorVal {
    conf: RequestConf;
    fullResponse: unknown;
    err?: Error;
    msg?: string;
    toast?: string;
    errno?: number;
    status?: number;
    statusCode?: number;
    response?: CommonResponseVal<unknown>;
}

export interface HttpRequestInstanceConf<T, U> {
    timeout: number; // 超时时间
    statusVariable: 'errno' | 'status'; // 用于判断 status 的变量
    unLoginStatus: Array<string | number>; // 未登录对应的 status
    successfulStatus: Array<string | number>; // 返回成功对应的 status
    header?: {
        [key: string]: string | number;
    };
    enableHttp2?: boolean; // 是否开启 http2

    unLoginCallback: (args: UnLoginReturnVal) => void; // 未登录触发的回调函数
    requestFailedCallback: (args: RejectErrorVal) => void; // 请求失败后触发的回调函数
    requestSucceedCallback: (args: AfterRequestReturnVal<T, U>) => void; // 请求成功后触发的回调函数

    confDecorateFn?: (conf: RequestConf) => Promise<RequestConf>; // 请求配置的装饰函数
    beforeRequestCallback?: (conf: RequestConf) => Promise<null>; // 请求触发时回调函数
    afterRequestCallback?: (args: AfterRequestReturnVal<T, U>) => void; // 请求后触发的回调函数
}

export type RequestReturnValType<T, U> = [RejectErrorVal | null, CommonResponseVal<T | U> | null];

export interface HttpRequestInstance<T, U> {
    request: (conf: RequestConf) => Promise<RequestReturnValType<T, U>>;
}
