import {getCurrentPages, addInterceptor} from '@tarojs/taro';
import {isObject} from '@baidu/health-utils';
import {init, send, customizeTrack} from '@baidu/health-ubc/lib/index.swan.esm';

import {getCommonSysInfo} from '../../taro';
import globalData from '../../../globalDataStore/globalData';
import {getDataForUbcAtom} from '../../../store/triageStreamAtom/index';
import {ONLINE_HOST} from '../../../constants/common';
import {API_HOST} from '../../../models/apis/host';

import {getUbcPageVal, NEED_COVER_PATH_MAP} from './utils';

import type {TrackCommonSendParams, UbcCommonSendParams} from './index.d';

const taroRuntime = require('@tarojs/runtime');

const isDev = process.env.NODE_ENV === 'development';

const ubcInit = () => async baseInfo => {
    const sysInfo = await getCommonSysInfo();
    const sid = sysInfo.sid || '';

    init('default', 12865, {
        sid,
        showLog: isDev,
        // 线下如dev环境打点上报到线下 切勿上报线上
        testMode: ONLINE_HOST.indexOf(API_HOST || '') === -1,
        strictMode: true,
        appVersion: '2.0.0',
        scene: baseInfo?.scene || '',
        enableTracking: true,
        trackReportCount: 5,
        pd: 'vita-mini-app',
        trackingOptions: {
            trackClick: true,
            trackXHR: {
                type: 'taro',
                interceptor: addInterceptor
            },
            trackPageVisibility: true,
            trackCustomize: true
        },
        taro: taroRuntime,
        pageSourceKeys: ['sessionId'],
        swan_name: 'baidujiankang',
        getCurrentPages: () => {
            const pages = getCurrentPages();

            if (!pages || pages.length === 0) {
                return [];
            }

            return pages.map(page => {
                const originalRoute = page?.path?.split('?')[0] || '';

                // 检查是否需要路径映射
                const mappedRoute = NEED_COVER_PATH_MAP[originalRoute] || originalRoute;

                return {route: mappedRoute};
            });
        }
    });
};

const ubcSend = () => async option => {
    let page = option.page || globalData.get('page')?.pageId;
    if (!page) {
        const {route} = getUbcPageVal();
        if (route) {
            page = route;
        }
    }

    isObject(option) &&
        send(
            {
                ...option,
                page
            },
            'default'
        );
};

// 通用的 ext 埋点值
const commonExtVal = {
    page_version: '2.0.0'
};

/**
 *
 * @description ubc 通用 view 事件埋点方法
 * @param params
 */
export const ubcCommonViewSend = (params: UbcCommonSendParams) => {
    if (!params?.value) {
        console.error('ubcCommonViewSend 发送出错：传参错误', params);

        return;
    }

    const pageIns = getUbcPageVal();
    const page = params?.page || pageIns?.route;
    const product_info = params?.ext?.product_info || {};

    const ubcAtomProductInfo = getDataForUbcAtom()?.product_info || {};

    const ext = {
        ...params?.ext,
        ...commonExtVal,
        product_info: {
            ...(pageIns?.options?.businessType
                ? {businessType: pageIns?.options?.businessType}
                : {}),
            ...(pageIns?.options?.doc_id ? {docId: pageIns?.options?.doc_id} : {}),
            ...ubcAtomProductInfo,
            ...product_info
        }
    };

    send(
        {
            ...params,
            ext,
            page,
            type: 'view'
        },
        'default'
    );
};

/**
 *
 * @description ubc 通用 clk 事件埋点方法
 * @param params
 */
export const ubcCommonClkSend = (params: UbcCommonSendParams) => {
    if (!params?.value) {
        console.error('ubcCommonClkSend 发送出错：传参错误', params);

        return;
    }

    const pageIns = getUbcPageVal();
    const page = params?.page || pageIns?.route;
    const product_info = params?.ext?.product_info || {};
    const ubcAtomProductInfo = getDataForUbcAtom()?.product_info || {};

    const ext = {
        ...params?.ext,
        ...commonExtVal,
        product_info: {
            ...(pageIns?.options?.businessType
                ? {businessType: pageIns?.options?.businessType}
                : {}),
            ...(pageIns?.options?.doc_id ? {docId: pageIns?.options?.doc_id} : {}),
            ...ubcAtomProductInfo,
            ...product_info
        }
    };

    send(
        {
            ...params,
            ext,
            page,
            type: 'clk'
        },
        'default'
    );
};

/**
 *
 * @description ubc 通用 timing 事件埋点方法
 * @param params
 */
export const ubcCommonTimingSend = (params: UbcCommonSendParams) => {
    if (!params?.value) {
        console.error('ubcCommonClkSend 发送出错：传参错误', params);

        return;
    }

    const pageIns = getUbcPageVal();
    const page = params?.page || pageIns?.route;
    const product_info = params?.ext?.product_info || {};
    const ubcAtomProductInfo = getDataForUbcAtom()?.product_info || {};

    const ext = {
        ...params?.ext,
        ...commonExtVal,
        product_info: {
            ...(pageIns?.options?.businessType
                ? {businessType: pageIns?.options?.businessType}
                : {}),
            ...ubcAtomProductInfo,
            ...product_info
        }
    };

    send(
        {
            ...params,
            ext,
            page,
            type: 'timing'
        },
        'default'
    );
};

/**
 *
 * @description track 自定义事件埋点方法
 * @param params
 */
export const trackCustomizeSend = (params: TrackCommonSendParams) => {
    customizeTrack(params);
};

export const ubcFn = {
    init: ubcInit(),
    send: ubcSend()
};

export const ubc = {
    ubcFn
};
