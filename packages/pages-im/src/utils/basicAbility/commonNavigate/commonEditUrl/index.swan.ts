import {H5_HOST} from '../../../../models/apis/host';
import {PATH_MAP} from '../../../../path/wenzhen';
import {OpenType} from '../main';

// 独立分包标识
const independentSubSign = '/vita';
const isDev = process.env.NODE_ENV === 'development';

// 需要轻浏览框架打开 list
const needEasybrowseList = [
    'pages/jiayi/purchase/index',
    'pages/middlePage/index',
    'wwwhos/h5#/', // 增值服务包手百场景都走轻框
    'mfollow/materials', // 患教资料H5
    'map.baidu.com', // 百度地图
    'health.baidu.com',
    'healthbusiness/pages/diseaseZone'
];

/**
 *
 * @description 处理需要使用手百轻框打开的页面
 * @param url url 地址
 * @param openType open 类型
 * @returns
 */
const dealneedEasybrowseList = (
    url: string,
    openType: OpenType
): {
    url: string;
    openType: OpenType;
    hasRewrited: boolean;
} => {
    try {
        let resUrl = url;
        let resOpenType = openType;

        needEasybrowseList.some(i => {
            if (url.includes(i)) {
                resUrl =
                    url.startsWith('http://') || url.startsWith('https://')
                        ? url
                        : `${H5_HOST}${(url.startsWith('/') ? url : `/${url}`) as string}`;

                resOpenType = 'easybrowse';

                return true;
            }
        });

        return {
            hasRewrited: true,
            url: resUrl,
            openType: resOpenType
        };
    } catch (err) {
        console.error('dealneedEasybrowseList 出错：', err);

        return {
            hasRewrited: false,
            url,
            openType
        };
    }
};

const setNewUrl = (url, openType) => {
    const _openType = openType;
    const _url = url;
    // let matches: string[] = [];
    const urlObj = _url.split('?');
    const path = urlObj[0].substring(0, 1) !== '/' ? `/${urlObj[0] as string}` : urlObj[0];
    // 老页面被改造后跳转新路由
    if (PATH_MAP[path]) {
        return {
            _url: `${independentSubSign}${PATH_MAP[path] as string}?${urlObj[1] as string}`,
            _openType
        };
    }
    // 目前打进mars的独立分包集合
    const independPath = 'wenzhen';
    const reg = new RegExp(
        // eslint-disable-next-line max-len
        `\\/?(healthbusiness|sporem|uha/ask|mfollow/materials|mall|tuanjian|vas|decision|aihub|${independPath})\\/pages[\\w-/]+`
    );

    if (reg.test(_url)) {
        return {_url, _openType};
    }

    // wenzhen | guahao Taro 跳转
    // 小程序单独处理下 匹配 /wenzhen/pages 开头 或者 wenzhen/pages 开头
    const match = /\/?(vita)\/pages([\w-/]+)/.exec(_url);

    if (match) {
        const [, prefix, suffix] = match;

        // 根据 isDev 环境变量构建 URL 前缀
        const prefixUrl = isDev ? '/pages' : `/${prefix}/pages`;

        // 获取原始 URL 中的查询参数部分
        const queryIndex = _url.indexOf('?');
        const queryPart: string = queryIndex !== -1 ? _url.substring(queryIndex + 1) : '';

        // 构建最终的 URL，包括查询参数
        return {
            _url: queryPart ? `${prefixUrl}${suffix}?${queryPart}` : `${prefixUrl}${suffix}`,
            _openType
        };
    }

    // 小程序跳转其他小程序
    if (/^baiduboxapp.*/.test(url)) {
        return {_url: url, _openType: 'otherMiniApp'};
    }

    return {_url, _openType};
};

export const commonRedirectUrlFn = ({
    url,
    openType
}): Promise<{url: string; openType: OpenType}> => {
    return new Promise((resolve, reject) => {
        try {
            let _url = url;
            let _openType = openType;

            // 如果是非轻浏览框架页面处理 url
            if (openType !== 'easybrowse') {
                const r = setNewUrl(url, openType);
                _url = r._url as string;
                _openType = r._openType as string;
            }

            // 处理需要轻框打开的地址
            const easybrowseRes = dealneedEasybrowseList(_url, _openType);
            if (easybrowseRes?.hasRewrited) {
                _url = easybrowseRes?.url;
                _openType = easybrowseRes?.openType;
            }

            if (_url?.startsWith('http') & _url.includes('/wenzhen')) {
                _url = _url.substring(url.indexOf('/wenzhen'));
            }

            try {
                resolve({
                    url: _url,
                    openType: _openType
                });
            } catch (err) {
                reject(err);
            }
        } catch (err) {
            console.error('commonRedirectUrlFn 出错：', err);
            reject(err);
        }
    });
};
