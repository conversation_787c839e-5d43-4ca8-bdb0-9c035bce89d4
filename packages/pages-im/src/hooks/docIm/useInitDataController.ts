import {useSet<PERSON><PERSON>} from 'jotai';
import {eventCenter} from '@tarojs/taro';
import {useCallback, useEffect, useRef, useState} from 'react';

import {
    clearSession,
    titleDataAtom,
    userData<PERSON>tom,
    inputData<PERSON>tom,
    lastMsgId<PERSON>tom,
    transData<PERSON>tom,
    getTransDataAtom,
    curSessionId<PERSON>tom,
    curWxLoginDataAtom,
    getCurSessionIdAtom,
    updateDataForUbcAtom,
    sessionCapsulesToolsAtom,
    renderSessionCapsulesAtom,
    sessionCapsulesToolsMd5Atom,
    updateTriageSessionHasHistoryMsgAtom
} from '../../store/docImAtom';
import {agreementInfoAtom} from '../../store/docImAtom/otherData';
import {createTriageStreamMsgAtom, updateCurSessionMsgIdsAtom} from '../../store/docImAtom/msg';

import {useGetUrlParams} from '../common';

import {MSG_CARDID_ENUM_STREAM_TYPE} from '../../constants/msg';
import {getAiMsgHistoryData, getTriageStreamCoreReq} from '../../models/services/docIm/';
import {getFkParamsAll, type FkParamsVal} from '../../utils/generalFunction/riskControler';

import type {MsgId, UrlParamsType} from '../../typings';
import type {
    CapsulesToolsType,
    SessionId,
    GetMsgListTransParams
} from '../../store/docImAtom/index.type.ts';

import {useGetSessionMsgIds} from './dataController';
import {updateSpecialCardAdjectiveMsgId} from './msgUtils';
import {useConversationDataController} from './useConversationDataController';

import type {AntiPassDataType} from './index.d';

/**
 * 获取核心数据
 *
 * @returns 如果请求成功，则返回核心数据；如果请求失败，则返回undefined
 */
const getCoreData = async (arg: {fkParams: FkParamsVal | undefined; urlParams: UrlParamsType}) => {
    const {fkParams, urlParams} = arg;

    const [err, data] = await getTriageStreamCoreReq({
        riskInfo: Object.assign(
            {},
            fkParams
                ? {
                    ...(fkParams as object),
                    v: ''
                }
                : {}
        ),
        sessionKey: urlParams?.sessionKey || '',
        sessionId: urlParams?.sessionId || '',
        ...(urlParams?.businessType ? {businessType: urlParams?.businessType} : {})
    });

    if (!err) {
        return data?.data;
    }
};

const getFkParams = async () => {
    let fkRes: FkParamsVal | null = null;
    if (!fkRes && process.env.TARO_ENV === 'h5') {
        fkRes = await getFkParamsAll({
            ak: '6810',
            ev: 'page_enter',
            dataApp:
                // eslint-disable-next-line
                'eyJhcHBfa2V5IjoiNjgxMCIsImFwcF92aWV3IjoicHJvbW90ZSIsImJyb3dzZXJfdXJsIjoiaHR0cHM6Ly9zb2ZpcmUuYmFpZHUuY29tL2RhdGEvdWEvYWIuanNvbiIsImZvcm1fZGVzYyI6IiIsInNlbmRfaW50ZXJ2YWwiOjUwLCJzZW5kX21ldGhvZCI6M30='
        });

        return fkRes;
    }

    return undefined;
};

/**
 * 初始化页面数据控制器
 *
 * @param id 会话ID
 * @returns 无返回值
 */
export const useInitDataController = () => {
    const [canRender, setCanRender] = useState(false);
    const [antiPassData, setAntiPassData] = useState<AntiPassDataType>();
    const [isLoadingFirstHistory, setIsLoadingFirstHistory] = useState(false);

    const sessionId = useRef<SessionId>();
    const getDataTriggeredRef = useRef(false);
    const isCreateSessionSceneRef = useRef(false);
    const hasLoadedConversionData = useRef(false);
    const initiatedSessionIdRef = useRef<SessionId | null>(null);
    const transDataRef = useRef<GetMsgListTransParams | undefined>(undefined);
    // 首次会话加载控制器；
    const holdPromiseRef = useRef<{promise: Promise<void>; resolve: (() => void) | null} | null>(
        null
    );

    const setUserData = useSetAtom(userDataAtom);
    const setInputData = useSetAtom(inputDataAtom);
    const setTitleData = useSetAtom(titleDataAtom);
    const setTransDataAtom = useSetAtom(transDataAtom);
    const setWxLoginData = useSetAtom(curWxLoginDataAtom);
    const setAgreementInfo = useSetAtom(agreementInfoAtom);
    const setCurSessionAtom = useSetAtom(curSessionIdAtom);
    const setSessionCapsulesTools = useSetAtom(sessionCapsulesToolsAtom);
    const setRenderSessionCapsules = useSetAtom(renderSessionCapsulesAtom);
    const setSessionCapsulesToolsMd5 = useSetAtom(sessionCapsulesToolsMd5Atom);

    const urlParams = useGetUrlParams();
    const {msgIds} = useGetSessionMsgIds();
    const curSessionId = getCurSessionIdAtom();
    const updateLastMsgId = useSetAtom(lastMsgIdAtom);
    const {createConversation} = useConversationDataController();

    /**
     *
     * @description 创建一个稳定的 Promise 变量，用于控制 conversion 数据加载执行逻辑。
     *
     */
    const createHoldPromise = () => {
        let resolveFn: (() => void) | null = null;
        const promise = new Promise<void>(resolve => {
            resolveFn = resolve;
        });

        holdPromiseRef.current = {promise, resolve: resolveFn};
    };

    /**
     *
     * @description 释放首次会话加载控制器，getFirstConversation 继续执行;
     */
    const releaseFirstConversation = () => {
        // eslint-disable-next-line no-console
        console.info('releaseFirstConversation 执行');

        if (holdPromiseRef.current?.resolve) {
            holdPromiseRef.current.resolve(); // 解析 Promise 使 getFirstConversation 继续执行
            holdPromiseRef.current = null; // 清空引用，防止重复调用
        }
    };

    const createMsgAtom = useCallback(
        (
            ids: MsgId[],
            msgData,
            ops: {
                type: 'unshift' | 'push';
                isFirstAutoPull?: boolean;
                triggerSymbol?: 'getCoreData' | 'getHistoryMsg';
                sourceApi?: 'conversation' | 'getHistory';
                extData?: {
                    hasHistoryMsg?: boolean;
                    isPullHistory?: boolean;
                };
            }
        ) => {
            if (!sessionId.current && !curSessionId) return;

            updateCurSessionMsgIdsAtom(ids, {
                type: ops.type || 'push'
            });

            const targetSessionId = sessionId.current || curSessionId || '';

            ids.forEach(id => {
                targetSessionId &&
                    createTriageStreamMsgAtom(`${targetSessionId}_${id}`, {
                        ...msgData[id],
                        meta: {
                            ...msgData[id]?.meta,
                            localExt: {
                                dataSource:
                                    ops.sourceApi === 'getHistory' ? 'history' : 'conversation',
                                insertType: ops.type,
                                needScrollToBottom: ops.type !== 'unshift' || ops.isFirstAutoPull
                            }
                        }
                    });
            });

            // 新增消息或者首次拉取历史消息场景下，更新特殊消息卡片有效状态；
            const newIds = [...ids];
            if (ops.type === 'unshift' && !ops.isFirstAutoPull) {
                newIds.reverse();
            }

            newIds.forEach(id => {
                updateSpecialCardAdjectiveMsgId(
                    msgData[id]?.data?.content?.cardId as MSG_CARDID_ENUM_STREAM_TYPE,
                    msgData[id]?.meta.msgId,
                    {type: ops.isFirstAutoPull ? 'push' : ops.type}
                );
            });

            (ops.type === 'push' || ops.isFirstAutoPull) && updateLastMsgId(ids[ids.length - 1]);

            setCanRender(true);
        },
        [curSessionId, updateLastMsgId]
    );

    // 更新会话胶囊工具；
    const updateSessionCapsulesTools = useCallback(
        (arg: {md5: string; list: CapsulesToolsType[]}) => {
            setSessionCapsulesToolsMd5(arg.md5);

            setSessionCapsulesTools(arg.list);
        },
        [setSessionCapsulesTools, setSessionCapsulesToolsMd5]
    );

    const getHistoryMsgFn = useCallback(
        async (arg: {sessionId: SessionId; currentMsgId?: MsgId}) => {
            const transData = transDataRef.current || getTransDataAtom() || undefined;
            const [err, data] = await getAiMsgHistoryData({
                size: 10,
                viewType: 'pre',
                sessionId: arg.sessionId,
                expertId: Number(urlParams?.expert_id || urlParams?.expertId),
                docId: urlParams?.doc_id || urlParams?.docId || '',
                currentMsgId: arg.currentMsgId,
                ...(transData ? {transData} : {}),
                ...(urlParams?.businessType ? {businessType: urlParams?.businessType} : {})
            });

            if (!err) {
                return data?.data;
            }
        },
        [
            urlParams?.businessType,
            urlParams?.docId,
            urlParams?.doc_id,
            urlParams?.expertId,
            urlParams?.expert_id
        ]
    );

    // 获取历史会话数据
    const getHistoryMsg = useCallback(
        async (arg: {sessionId: SessionId; currentMsgId?: MsgId; isFirstAutoPull?: boolean}) => {
            try {
                setIsLoadingFirstHistory(true);
                const res = await getHistoryMsgFn({
                    sessionId: arg.sessionId,
                    ...(arg.currentMsgId ? {currentMsgId: arg.currentMsgId} : {})
                });

                setIsLoadingFirstHistory(false);
                const {hasMore, msgData, msgIds: msgIdsHistory} = res || {};

                // 自动拉取后，仍有历史消息；
                updateTriageSessionHasHistoryMsgAtom(arg.sessionId, hasMore || false);

                msgIdsHistory?.length &&
                    msgData &&
                    createMsgAtom(msgIdsHistory, msgData, {
                        type: 'unshift',
                        isFirstAutoPull: arg.isFirstAutoPull,
                        triggerSymbol: 'getHistoryMsg',
                        sourceApi: 'getHistory',
                        extData: {
                            hasHistoryMsg: hasMore
                        }
                    });

                // 有历史消息，渲染胶囊工具
                msgIdsHistory?.length && setRenderSessionCapsules(true);

                if (res?.transData) {
                    transDataRef.current = res?.transData || undefined;
                    setTransDataAtom(res?.transData);
                }

                !!arg.isFirstAutoPull &&
                    eventCenter.trigger('triageStreamScrollToMsg', {symbol: 'getHistoryMsg'});
            } catch (err) {
                setIsLoadingFirstHistory(false);
                console.error('获取历史消息失败', err);
            }
        },
        [createMsgAtom, getHistoryMsgFn, setRenderSessionCapsules, setTransDataAtom]
    );

    // 获取 core 数据
    const getData = useCallback(async () => {
        // eslint-disable-next-line no-async-promise-executor, complexity
        return new Promise(async (resolve, reject) => {
            try {
                const fkParams = await getFkParams();

                setTransDataAtom(undefined);

                const r = await getCoreData({
                    fkParams,
                    urlParams
                });

                let cancelConversation = false;

                if (r?.session) {
                    const {sessionId: id, stopConversation = false} = r?.session || {};
                    sessionId && clearSession(id, msgIds || []);

                    sessionId.current = id;
                    setCurSessionAtom(id);
                    getDataTriggeredRef.current = true;
                    cancelConversation = stopConversation;

                    createHoldPromise();

                    updateDataForUbcAtom({
                        sessionId: id
                    });

                    // 在 H5 环境下，将 sessionId 更新到 URL 参数中，用于跳转其他页面后返回重新加载数据的场景；@wanghaoyu08
                    if (process.env.TARO_ENV === 'h5') {
                        const url = new URL(window.location.href);
                        url.searchParams.set('sessionId', id);
                        window.history.replaceState({}, '', url.toString());
                    }
                }

                if (r?.transData) {
                    transDataRef.current = r?.transData || undefined;
                    setTransDataAtom(r?.transData);
                }

                if (r?.sessionData) {
                    setWxLoginData(r?.sessionData?.control);
                }

                if (r?.msgIds) {
                    createMsgAtom(r?.msgIds, r?.msgData, {
                        type: 'push',
                        triggerSymbol: 'getCoreData',
                        sourceApi: 'conversation',
                        extData: {
                            hasHistoryMsg: r?.session?.hasMorePreMsg,
                            isPullHistory: r?.session?.isPullHistory
                        }
                    });
                }

                if (r?.userData) {
                    setUserData(r?.userData);
                }

                if (r?.inputData) {
                    setInputData(r?.inputData);
                }

                if (r?.titleData) {
                    setTitleData(r?.titleData);
                }

                if (r?.toolData?.capsules) {
                    updateSessionCapsulesTools(r?.toolData?.capsules);
                }

                if (r?.bottomTips?.agreement) {
                    setAgreementInfo(r?.bottomTips?.agreement);
                }

                // Tips：风控场景数据临时处理，暂不耦合全局弹窗逻辑；@wanghaoyu08
                if (
                    process.env.TARO_ENV === 'h5' &&
                    fkParams &&
                    r?.imActionData?.actionData?.actionType === 'antipass'
                ) {
                    // eslint-disable-next-line no-console
                    console.warn('命中风控，需用户手动验证');

                    setAntiPassData({
                        data: r?.imActionData?.actionData,
                        refreshParams: {
                            sessionId: r?.session?.sessionId
                        },
                        fkParams
                    });

                    createHoldPromise();
                } else {
                    // eslint-disable-next-line no-console
                    console.info('未命中风控数据');
                    !r?.session?.isPullHistory && !cancelConversation && releaseFirstConversation();
                }

                if (r?.session?.hasMorePreMsg) {
                    if (r?.session?.isPullHistory) {
                        // 如果存在更多历史消息，且需要自动拉取，则需要拉取历史消息；
                        createHoldPromise();

                        await getHistoryMsg({
                            sessionId: r?.session?.sessionId,
                            isFirstAutoPull: true
                        });
                        !cancelConversation && releaseFirstConversation();
                    } else {
                        updateTriageSessionHasHistoryMsgAtom(r?.session?.sessionId, true);
                    }
                }
                resolve(r);
            } catch (error) {
                reject(error);
            }
        });
    }, [
        msgIds,
        urlParams,
        setTitleData,
        createMsgAtom,
        getHistoryMsg,
        setCurSessionAtom,
        setInputData,
        setTransDataAtom,
        setUserData,
        setWxLoginData,
        setAgreementInfo,
        updateSessionCapsulesTools
    ]);

    const getFirstConversation = useCallback(
        async (arg?: {isCreateSessionScene: boolean}) => {
            // eslint-disable-next-line no-console
            console.info('首次会话数据加载');
            const isCreateSessionScene = arg?.isCreateSessionScene;

            if (holdPromiseRef.current) {
                await holdPromiseRef.current.promise;
            }

            hasLoadedConversionData.current = true;
            const transData = transDataRef.current || getTransDataAtom() || undefined;

            await createConversation({
                msg: {
                    type: 'text',
                    content: '',
                    sceneType:
                        urlParams?.query || urlParams?.word ? 'queryFirstCall' : 'defaultFirstCall'
                },
                withOutMsg: true,
                ctrlData: {
                    firstCall: true,
                    ...(isCreateSessionScene ? {newSession: 1} : {})
                },
                ...(transData ? {transData} : {})
            });
        },
        [createConversation, urlParams?.query, urlParams?.word]
    );

    useEffect(() => {
        if (
            urlParams?.sessionId &&
            sessionId.current !== urlParams?.sessionId &&
            (process.env.TARO_ENV === 'weapp' || process.env.TARO_ENV === 'swan')
        ) {
            createHoldPromise();
        }

        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    useEffect(() => {
        return () => {
            hasLoadedConversionData.current = false;
        };
    }, []);

    // Tips：为保证渲染时序，在会话ID存在时，才执行首次会话创建；
    // 创建历史会话数据时，先加载历史会话数据，再创建对话数据
    useEffect(() => {
        if (
            curSessionId &&
            curSessionId !== initiatedSessionIdRef.current &&
            getDataTriggeredRef.current
        ) {
            initiatedSessionIdRef.current = curSessionId;
            getDataTriggeredRef.current = false;
            getFirstConversation({
                isCreateSessionScene: isCreateSessionSceneRef.current
            });
            isCreateSessionSceneRef.current = false;
        }
    }, [getFirstConversation, curSessionId]);

    return {
        canRender,
        antiPassData,
        curSessionId,
        isLoadingFirstHistory,
        getData,
        getHistoryMsg,
        releaseFirstConversation
    };
};
