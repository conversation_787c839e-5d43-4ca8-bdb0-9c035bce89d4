import {useCallback} from 'react';

import {useGetSessionId, useInsertMsg} from '../pageDataController';
import {useConversationDataController} from '../useConversationDataController';

import {updateTriageStreamMsgAtom} from '../../../store/docImAtom/msg';

import type {InteractionInfo} from '../../../typings';
import type {MsgItemType} from '../../../store/docImAtom/index.type';

import {genImMsgKey} from '../../../utils';
import httpRequest from '../../../utils/basicAbility/comonRequest/cui';

import type {GetUseractionRespType} from '../../../models/services/docIm/index.d';
import type {SceneTypeOfParams} from '../../../models/services/docIm/sse/index.d';

export const useCapsuleToolsController = () => {
    const {mockUserMsg, cancelPreSSE, createConversation} = useConversationDataController();
    const sessionId = useGetSessionId();
    const {insertMsg} = useInsertMsg();

    /**
     * @description 更新mock数据的状体
     * @param msgContent mock数据结构
     */
    const updateMsgStatus = useCallback(
        (msgContent, msgKey) => {
            const updatedMsg: MsgItemType<unknown> = {
                ...msgContent,
                meta: {
                    ...msgContent?.meta,
                    localMsgStatus: 'success'
                }
            };

            // eslint-disable-next-line @typescript-eslint/restrict-template-expressions
            updateTriageStreamMsgAtom(`${sessionId}_${msgKey}`, updatedMsg, {
                _debugSymbol: 'useCapsuleToolsController'
            });
        },
        [sessionId]
    );

    /**
     * @description 点击问诊tools调用接口，插入假消息
     */
    const updateWzMockMessage = async (interactionInfo: InteractionInfo) => {
        try {
            const msgKey = genImMsgKey(10);
            const m = mockUserMsg(
                {
                    type: 'text',
                    content: '我想购买在线咨询服务',
                    sceneType: 'wzBotCapsule_wenzhen'
                },
                msgKey
            );

            const {url, method} = interactionInfo;
            const r = await httpRequest<GetUseractionRespType<'capsuleClick'>>({
                url: url as string,
                method,
                data: {
                    bizActionType: 'capsuleClick',
                    chatData: {
                        sessionId
                    },
                    bizActionData: {
                        clickCapsuleInfo: {
                            msgKey,
                            content: '我想购买在线咨询服务'
                        }
                    }
                }
            });
            updateMsgStatus(m, msgKey);
            cancelPreSSE('用户点击胶囊工具，主动停止SSE');
            const [err, res] = r;
            if (err) return;
            const data = res?.data;
            const {message} = data || {};

            message?.forEach(i => {
                insertMsg(i.meta.msgId, i);
            });
        } catch (error) {
            console.error(error, 'error');
        }
    };

    /**
     * @description 问诊消息发送
     */
    const updateWzMessage = useCallback(
        async (
            interactionInfo: InteractionInfo,
            ops: {sceneType: SceneTypeOfParams},
            intent?: string
        ) => {
            try {
                const {params = {}, sceneType = ''} = interactionInfo;
                const {payload = []} = params || {payload: []};
                let contentStr = '';
                (payload as Array<unknown>).forEach(item => {
                    if ((item as Record<string, string>)?.content) {
                        contentStr += (item as Record<string, string>)?.content;
                    }
                });
                if (contentStr) {
                    await createConversation({
                        msg: {
                            type: 'text',
                            content: contentStr,
                            sceneType: (sceneType as SceneTypeOfParams) || ops?.sceneType
                        },
                        intent
                    });
                }
            } catch (error) {
                console.error(error);
            }
        },
        [createConversation]
    );

    return {
        updateWzMockMessage,
        updateWzMessage
    };
};
