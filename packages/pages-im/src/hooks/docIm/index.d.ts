import type {MSG_CARDID_TYPE, MSG_CARDID_ENUM_STREAM_TYPE} from '../../constants/msg';

import type {MsgId, Qid, InteractionInfo} from '../../typings';
import type {SessionId, GetMsgListTransParams} from '../../store/docImAtom/index.type';
import {type FkParamsVal} from '../../utils/generalFunction/riskControler';
import type {
    InputImgMap,
    ConversationSSEParams,
    SceneTypeOfParams
} from '../../models/services/docIm/sse/index.d';
import {MsgType, RichTextContent} from '../triageStream';
import {IPicProps} from '../../typings/upload';

export {SceneTypeOfParams};

type Para = ConversationSSEParams['params'];
export interface CreateConversationArgs {
    msg: Omit<Para['msg']['payload'][0], 'msgKey'> &
        (
            | {
                  type: Exclude<MsgType, 'richText'>;
                  preData?: IPicProps[];
                  sceneType: SceneTypeOfParams;
                  content: string;
              }
            | {
                  type: Extract<MsgType, 'richText'>;
                  preData?: IPicProps[];
                  sceneType?: SceneTypeOfParams;
                  content: RichTextContent;
              }
        );
    withOutMsg?: boolean;
    withOutThinkingMsg?: boolean;
    ctrlData?: {
        firstCall?: boolean;
    };
    transData?: GetMsgListTransParams;
    intent?: string;
}

export interface ConvertParamsType {
    cardId: MSG_CARDID_TYPE | MSG_CARDID_ENUM_STREAM_TYPE;
    content?: {
        value: string | InputImgMap;
        origin?: string;
    };
    ext: {
        msgKey: MsgId;
        sessionId: SessionId;
    };
}

export interface AntiPassDataType {
    refreshParams: {
        sessionId?: string;
        qid?: Qid;
    };
    data: {
        interaction: string;
        interactionInfo: InteractionInfo;
    };
    fkParams: FkParamsVal;
}
