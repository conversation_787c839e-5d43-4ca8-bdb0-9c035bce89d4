import {API_HOST} from '../../apis/host';
import {
    getTriageStreamCore,
    getUseraction,
    getAiMsgHistory,
    getUserBizAction
} from '../../apis/cui';

import httpRequest from '../../../utils/basicAbility/comonRequest/cui';

import type {
    GetTriageStreamCoreRespType,
    GetTriageStreamCoreParamsType,
    GetUseractionParamsType,
    BizActionType,
    WzBizActionType,
    GetUseractionRespType,
    GetWxLoginSessionParams,
    GetWxLoginSessionRes,
    GetAiMsgHistoryResp,
    GetAiMsgHistoryParams,
    GetUserBizActionRespType,
    GetUserBizActionParamsType
} from './index.d';

/**
 *  获取诊前 Stream 页面核心数据V2
 *
 * @param {GetTriageStreamCoreParamsType} params 请求参数
 * @returns {GetTriageStreamCoreRespType} 返回请求的结果
 */
export const getTriageStreamCoreReq = (params: GetTriageStreamCoreParamsType) => {
    return httpRequest<GetTriageStreamCoreRespType>({
        url: `${API_HOST}${getTriageStreamCore}`,
        method: 'POST',
        data: params,
        isFirstScreen: true,
        needTransparentWhiteListParams: true
    });
};

/**
 *  获取用户行为操作后的卡片数据
 *
 * @param {GetUseractionParamsType} params 请求参数
 * @returns {GetUseractionRespType} 返回请求的结果
 */
export const getUseractionReq = <T extends BizActionType>(params: GetUseractionParamsType<T>) => {
    return httpRequest<GetUseractionRespType<T>>({
        url: `${API_HOST}${getUseraction}`,
        method: 'POST',
        data: params,
        isFirstScreen: false,
        needTransparentWhiteListParams: true
    });
};

/**
 * 获取微信跳转行为
 */
export const getWxJumpReq = (params: GetWxLoginSessionParams) => {
    return httpRequest<GetWxLoginSessionRes>({
        url: `${API_HOST}${params?.url || ''}`,
        method: 'POST',
        data: params?.params,
        isFirstScreen: false,
        needTransparentWhiteListParams: true
    });
};

/**
 * 获取ai诊前历史消息
 */
export const getAiMsgHistoryData = (params: GetAiMsgHistoryParams) => {
    return httpRequest<GetAiMsgHistoryResp>({
        url: `${API_HOST}${getAiMsgHistory}`,
        method: 'POST',
        data: params,
        isFirstScreen: false,
        needTransparentWhiteListParams: true
    });
};

/**
 *  获取用户行为操作后的卡片数据
 *
 * @param {GetUseractionParamsType} params 请求参数
 * @returns {GetUseractionRespType} 返回请求的结果
 */
export const getUserBizActionReq = <T extends WzBizActionType>(
    params: GetUserBizActionParamsType<T>
) => {
    return httpRequest<GetUserBizActionRespType<T>>({
        url: `${API_HOST}${getUserBizAction}`,
        method: 'POST',
        data: params,
        isFirstScreen: false,
        needTransparentWhiteListParams: true
    });
};

//点击『免费咨询医生』请求接口---------待实现
export const getFreeConsultDoctor = (url, params) => {
    return httpRequest({
        url: url,
        method: 'POST',
        data: params,
        isFirstScreen: false,
        needTransparentWhiteListParams: true
    });
};
