import {SSEProcessor} from 'sse-kit/lib/bundle.h5.esm';

import {API_HOST} from '../../../../models/apis/host';
import {createTriageStreamConversation} from '../../../apis/cui';

import editConf from '../../../../utils/basicAbility/comonRequest/utils/editConf';

import type {SSEResponseType} from '../../../../store/docImAtom/index.type';

export type SSEProcessorInstance<T extends object> = InstanceType<typeof SSEProcessor<T>>;

export const conversationSSE = async (params): Promise<SSEProcessorInstance<SSEResponseType>> => {
    // TODO：重新整理装饰请求 conf 函数；@wanghaoyu08；
    const conf = {
        url: `${API_HOST}${createTriageStreamConversation}`,
        method: 'POST' as const,
        data: params,
        needTransparentWhiteListParams: true
    };
    const decoratedConf = await editConf(conf);

    return new SSEProcessor({
        url: decoratedConf.url as `https://${string}`,
        method: 'POST',
        timeout: 300000,
        enableConsole: false,
        headers: decoratedConf.header as Headers,
        reqParams: decoratedConf.data,
        onComplete: params.onComplete,
        onError: params.onError,
        onHeadersReceived: params.onHeadersReceived
    });
};
