import {Base64} from 'js-base64';
import {SSEProcessor} from 'sse-kit/lib/bundle.swan.esm';

import {API_HOST} from '../../../../models/apis/host';
import {createTriageStreamConversation} from '../../../apis/cui';

import editConf from '../../../../utils/basicAbility/comonRequest/utils/editConf';

import type {SSEResponseType} from '../../../../store/docImAtom/index.type';

export type SSEProcessorInstance<T extends object> = InstanceType<typeof SSEProcessor<T>>;

export const conversationSSE = async (params): Promise<SSEProcessorInstance<SSEResponseType>> => {
    // TODO：重新整理装饰请求 conf 函数；@wanghaoyu08；
    const conf = {
        url: `${API_HOST}${createTriageStreamConversation}?_format=base64`,
        method: 'POST' as const,
        data: params,
        needTransparentWhiteListParams: true
    };
    const decoratedConf = await editConf(conf);

    return new SSEProcessor({
        url: decoratedConf.url as `https://${string}`,
        method: 'POST',
        enableConsole: false,
        timeout: 300000,
        headers: decoratedConf.header as Headers,
        reqParams: decoratedConf.data,
        onComplete: params.onComplete,
        onError: params.onError,
        onHeadersReceived: params.onHeadersReceived,
        preprocessDataCallback
    });
};

function decodeBase64Segments(input: string): string {
    try {
        return Base64.decode(input);
    } catch (error) {
        console.error('解码出错，返回原始匹配内容:', input, error);

        return input;
    }
}

function preprocessDataCallback(input: string): string {
    if (input.startsWith('data:')) {
        const line = decodeBase64Segments(input.replace(/^data:/, '').trim());

        return `data: ${line}`;
    }

    return input;
}
