import {atom, createStore} from 'jotai';
import {atomFamily} from 'jotai/utils';

import type {MsgId, Qid} from '../../typings';

import {curSessionMsgIdsAtom, resetDocImMsgAtom} from './msg';

import type {
    SessionId,
    CapsulesToolsType,
    UserDataType,
    InputDataType,
    DataForUbcType,
    WxLoginData,
    TitleDataType,
    GetMsgListTransParams
} from './index.type';

import {agreementInfoAtom} from './otherData';

// AI 分身页面的 Jotai Store
export const docImAtomStore = createStore();

// 当前会话 Qid
export const curSessionQidAtom = atom<Qid>('');
curSessionQidAtom.debugLabel = 'curSessionQidAtom';

// 当前会话 ID，用于区分会话
export const curSessionIdAtom = atom<SessionId>();
curSessionIdAtom.debugLabel = 'curSessionIdAtom';

// 当前会话的 wxLoginData
export const curWxLoginDataAtom = atom<WxLoginData>();
curWxLoginDataAtom.debugLabel = 'curWxLoginDataAtom';

// 有效的定向 sku 卡片 MsgId；无效需置灰展示；
export const adjectiveDirectedSkuMsgIdAtom = atom<MsgId[]>([]);
adjectiveDirectedSkuMsgIdAtom.debugLabel = 'adjectiveDirectedSkuMsgIdAtom';

// 最后一条消息 ID
export const lastMsgIdAtom = atom<MsgId>('');
lastMsgIdAtom.debugLabel = 'lastMsgIdAtom';

// 最后一条 Conversion 返回的消息 ID
export const lastConversionMsgIdAtom = atom<MsgId>('');
lastMsgIdAtom.debugLabel = 'lastConversionMsgIdAtom';

// 会话胶囊工具列表
export const sessionCapsulesToolsAtom = atom<CapsulesToolsType[]>([]);
sessionCapsulesToolsAtom.debugLabel = 'sessionCapsulesToolsAtom';
export const sessionCapsulesToolsMd5Atom = atom<string>();
sessionCapsulesToolsMd5Atom.debugLabel = 'sessionCapsulesToolsMd5Atom';
// 是否渲染会话胶囊，控制胶囊时序，等conversion或msgHistory返回后再渲染胶囊
export const renderSessionCapsulesAtom = atom<boolean>(false);
renderSessionCapsulesAtom.debugLabel = 'renderSessionCapsulesAtom';

// 输入框是否hold
export const textareaHoldAtom = atom<boolean>(true);
textareaHoldAtom.debugLabel = 'textareaHoldAtom';

// 输入框是否聚焦
export const textareaFocusAtom = atom<boolean>(false);
textareaFocusAtom.debugLabel = 'textareaFocusAtom';

export const textareaPlaceholder = atom<string>('有什么健康问题尽管问我');
textareaPlaceholder.debugLabel = 'textareaPlaceholder';

// 会话生成状态，是否正在生成
export const isGeneratingAtom = atom<boolean>(false);
isGeneratingAtom.debugLabel = 'isGeneratingAtom';

// 是否由用户主动点击中断按钮终止对话
export const isUserInterruptedAtom = atom<boolean>(false);
isUserInterruptedAtom.debugLabel = 'isUserInterruptedAtom';

// 滚动到底部按钮是否可见
export const scrollToBottomBtnVisibleAtom = atom<boolean>(false);
scrollToBottomBtnVisibleAtom.debugLabel = 'scrollToBottomBtnVisibleAtom';

// 图片来源
export const imageSourceAtom = atom<string>('');
imageSourceAtom.debugLabel = 'imageSourceAtom';

// tips弹框提示数据
export const tipsDataAtom = atom<CapsulesToolsType['instruction'] | undefined>();

// 登录用户信息数据
export const userDataAtom = atom<UserDataType>({
    isLogin: false,
    avatar: '',
    name: ''
});
userDataAtom.debugLabel = 'userDataAtom';

// 登录用户信息数据
export const inputDataAtom = atom<InputDataType>({
    bottomTips: '',
    loadingTips: '',
    uploadImg: 0,
    uploadImgInstruction: undefined
});
inputDataAtom.debugLabel = 'inputDataAtom';

// UBC 数据埋点通用字段
export const dataForUbcAtom = atom<DataForUbcType>();
dataForUbcAtom.debugLabel = 'dataForUbcAtom';

// 用于透传到后端的数据
export const transDataAtom = atom<GetMsgListTransParams | undefined>(undefined);
transDataAtom.debugLabel = 'transDataAtom';

export const getTransDataAtom = () => {
    return docImAtomStore.get(transDataAtom);
};

// 更新 UBC 数据埋点通用字段
export const updateDataForUbcAtom = (data: DataForUbcType['product_info']) => {
    const state = docImAtomStore.get(dataForUbcAtom);
    const mergeData: DataForUbcType = {
        ...state,
        product_info: {
            ...state?.product_info,
            ...data
        }
    };

    docImAtomStore.set(dataForUbcAtom, mergeData);

    return dataForUbcAtom;
};

export const getDataForUbcAtom = () => {
    return docImAtomStore.get(dataForUbcAtom);
};

export const getUserInterruptedAtom = () => {
    docImAtomStore.get(isUserInterruptedAtom);
};

export const resetSessionIdAtom = () => {
    docImAtomStore.set(curSessionIdAtom, undefined);
};

export const setIsGeneratingAtom = (value: boolean) => {
    docImAtomStore.set(isGeneratingAtom, value);
};

export const setIsUserInterruptedAtom = (value: boolean) => {
    docImAtomStore.set(isUserInterruptedAtom, value);
};

/**
 * 用于存储会话历史消息状态的Atom
 *
 * @param id 会话ID
 * @returns 返回存储会话历史消息状态的Atom
 */
export const triageSessionHasHistoryMsgAtom = atomFamily((id: SessionId) => {
    const sessionHistoryAtom = atom<boolean>(false);
    sessionHistoryAtom.debugLabel = `triageSessionHasHistoryMsgAtom_${id}`;

    return sessionHistoryAtom;
});

/**
 * 更新会话历史消息状态的Atom
 *
 * @param id 会话ID
 * @param hasHistoryMsg 是否存在历史消息
 */
export const updateTriageSessionHasHistoryMsgAtom = (id: SessionId, hasHistoryMsg: boolean) => {
    const atomInstance = triageSessionHasHistoryMsgAtom(id);
    docImAtomStore.set(atomInstance, hasHistoryMsg);
};

export const getSessionCapsulesToolsMd5Atom = () => {
    return docImAtomStore.get(sessionCapsulesToolsMd5Atom);
};

// 获取curSessionId
export const getCurSessionIdAtom = () => {
    return docImAtomStore.get(curSessionIdAtom);
};

// 初始化时清理函数，微信&手百小程序会有缓存
export const clearSession = (oldSessionId: SessionId, msgIds?: MsgId[]) => {
    if (!oldSessionId) throw new Error('oldSessionId is required');

    // 1. 先暂停输入，防止用户继续操作
    docImAtomStore.set(textareaHoldAtom, true);
    docImAtomStore.set(textareaFocusAtom, false);
    docImAtomStore.set(textareaPlaceholder, '有什么健康问题尽管问我');

    // 2. 清理旧会话院子
    msgIds?.length &&
        msgIds.forEach(item => {
            resetDocImMsgAtom(`${oldSessionId}_${item}`);
        });

    // 3. 重置消息相关状态
    docImAtomStore.set(lastMsgIdAtom, '');
    docImAtomStore.set(lastConversionMsgIdAtom, '');
    docImAtomStore.set(adjectiveDirectedSkuMsgIdAtom, []);
    docImAtomStore.set(curSessionMsgIdsAtom, {
        type: 'delete',
        payload: docImAtomStore.get(curSessionMsgIdsAtom)
    });

    // 4. 重置其他 UI 状态
    docImAtomStore.set(sessionCapsulesToolsAtom, []);
    docImAtomStore.set(imageSourceAtom, '');
    docImAtomStore.set(scrollToBottomBtnVisibleAtom, false);
    docImAtomStore.set(tipsDataAtom, undefined);
    docImAtomStore.set(agreementInfoAtom, undefined);
    docImAtomStore.set(sessionCapsulesToolsMd5Atom, undefined);
    docImAtomStore.set(renderSessionCapsulesAtom, false);
    docImAtomStore.set(isGeneratingAtom, false);
    docImAtomStore.set(isUserInterruptedAtom, false);

    docImAtomStore.set(inputDataAtom, {
        bottomTips: '',
        loadingTips: '',
        uploadImg: 0,
        uploadImgInstruction: undefined
    });
};

// title信息数据
export const titleDataAtom = atom<TitleDataType>({
    title: ''
});
titleDataAtom.debugLabel = 'titleDataAtom';
export const getTitleDataAtom = () => {
    return docImAtomStore.get(titleDataAtom);
};
