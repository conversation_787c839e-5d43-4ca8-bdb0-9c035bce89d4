.ImInputSwanContainer {
    position: relative;
    background: inherit;
    background-color: #eff3f9;

    &PaidMed {
        background-color: #e8f0ff;
    }

    .fadeTransition {
        z-index: 10;
        opacity: 0;
        max-height: 0; /* 初始隐藏状态 */
    }

    .fadeVisible {
        opacity: 1;
        max-height: 500px; /* 设置足够大的值，确保能容纳内容 */
        transition:
            opacity 300ms ease-in-out,
            max-height 300ms ease-in-out;
    }

    .featureCom {
        align-items: flex-end;

        .imInputMain {
            position: relative;
            min-height: 180px;
            background-color: #fff;
            border-radius: 66px;
            border: 0.5px solid #d9dfe7;
            width: 100%;
            box-shadow: 0 0 30px 0 #d5dde9;

            .textarea {
                padding-left: 24px;
                width: 100%;
            }

            .textareaAutoHeight {
                height: 60px;
            }

            .textareaPlaceholder {
                font-size: 48px;
                color: #848691;
            }
        }

        .isSwanMsgInputMain {
            position: relative;
            min-height: 120px;
            background-color: #fff;
            border-radius: 66px;
            width: 100%;

            .textarea {
                padding-left: 24px;
                width: 100%;
                max-height: 192px;
                overflow: auto;
            }

            .textareaPlaceholder {
                font-size: 48px;
                color: #848691;
            }

            .textareaAutoHeight {
                height: 60px;
            }

            .swanMsgTextareaPlaceholder {
                font-size: 48px;
                color: #848691;
            }

            .bigFontSizeClass {
                font-size: 30px;
                color: #848691;
            }
        }

        .hasImgInputMain {
            border-top-left-radius: 0;
            border-top-right-radius: 0;
            border-top: 0.33px solid #e0e0e0;
        }

        .hasImgSwanMsgInputMain {
            border-top-right-radius: 0;
            border-top-left-radius: 0;
            border-top: 0.33px solid #e0e0e0;
            padding: 20px 42px;
        }

        .sendBtn {
            width: 240px;
            height: 132px;
            font-size: 48px;
            line-height: 48px;
            padding: 0;
            background: linear-gradient(to left, #00d3ea, #00cfa3);
            margin-left: 45px;
            font-weight: 700;
        }

        .sendBtnQC {
            width: 240px;
            height: 132px;
            font-size: 48px;
            line-height: 48px;
            padding: 0;
            background: linear-gradient(225deg, #4d69f0, #a767f5) !important;
            margin-left: 45px;
            font-weight: 700;
            border-color: transparent;
        }

        .disableBtn {
            opacity: 0.5;
        }

        .isSwanMsgSendBtn {
            width: 220px;
            height: 100px;
            font-size: 42px;
            line-height: 42px;
            padding: 0;
            background: linear-gradient(to left, #00d3ea, #00cfa3);
            margin-left: 42px;
            margin-bottom: 10px;
            font-weight: 700;
        }
    }

    .stopGenerateImage {
        padding-left: 22.5px;
        padding-right: 5px;
        position: relative;
        right: 0;
        bottom: 0;

        .stopGenerateIcon {
            width: 84px;
            height: 84px;
        }
    }
}

.ImInputWeappContainer {
    background-color: #fff;
    position: relative;

    .featureCom {
        align-items: flex-end;

        .imInputMain {
            min-height: 180px;
            background-color: #eef3f9;
            border-radius: 66px;
            flex: 1;
            align-items: flex-end;
            width: 924px;

            .textarea {
                height: 100px;
                width: 724px;
                align-items: baseline;
            }
        }

        .sendBtn,
        .sendBtnQC {
            width: 180px;
            height: 132px;
            padding: 0;
            font-weight: 700;
        }

        .placeholderContainer {
            line-height: 132px;
        }
    }
}

.ImInputWebContainer {
    background-color: #eef3f9;
    position: relative;

    &PaidMed {
        background-color: #e8f0ff;
    }

    .fadeTransition {
        z-index: 10;
        opacity: 0;
        max-height: 0; /* 初始隐藏状态 */
    }

    .fadeVisible {
        opacity: 1;
        max-height: 500px; /* 设置足够大的值，确保能容纳内容 */
        transition:
            opacity 300ms ease-in-out,
            max-height 300ms ease-in-out;
    }

    .stopGenerateImage {
        padding-left: 22.5px;
        padding-right: 5px;
        position: relative;
        right: 0;
        bottom: 0;

        .stopGenerateIcon {
            width: 84px;
            height: 84px;
        }
    }

    .featureCom {
        .imInputMain {
            min-height: 180px;
            background-color: #fff;
            border-radius: 66px;
            width: 100%;
            border: 0.5px solid #d9dfe7;

            .textarea {
                height: 100%;
                width: 686px;
                width: 100%;
                align-items: baseline;
                resize: none;
            }
        }

        .hasImgBorder {
            border-top-right-radius: 0;
            border-top-left-radius: 0;
            border-top: 0.33px solid #e0e0e0;
        }

        .sendBtn {
            width: 240px;
            height: 132px;
            font-size: 48px;
            line-height: 1.5;
            padding: 0;
            background: linear-gradient(to left, #00d3ea, #00cfa3);
            margin-left: 45px;
            font-weight: 700;
        }

        .sendBtnQC {
            width: 240px;
            height: 132px;
            font-size: 48px;
            line-height: 48px;
            padding: 0;
            background: linear-gradient(225deg, #4d69f0, #a767f5) !important;
            margin-left: 45px;
            font-weight: 700;
            border-color: transparent;
        }

        .disableBtn {
            opacity: 0.5;
        }

        .placeholderContain {
            flex: 1;
            border: none;
            outline: none;
            background-color: transparent;
            font-size: 48px;
            line-height: 66px;

            ::placeholder {
                background-color: transparent;
                color: #848691;
            }
        }
    }
}
