.sendVoiceBtn {
    padding: 54px 159px;
    padding-bottom: 21px;
    flex-direction: column;

    .sendVoiceText {
        font-size: 39px;
        color: #979797;
    }

    .sendBtnItem {
        width: 100%;
        height: 132px;
        border-radius: 66px;
        padding: 0;
    }

    .cancelTextColor {
        color: #fd503e;
    }

    .cancelBackgroundColor {
        background-color: #fd503e;
        border: 1px solid #fd503e;
    }

    .voiceIngGif {
        background-image: url('https://med-fe.cdn.bcebos.com/wenzhen-mini-app/voicegreen.gif');
        background-repeat: no-repeat;
        background-size: cover;
        width: 100%;
        height: 100%;
    }

    .voiceCancelGif {
        background-image: url('https://med-fe.cdn.bcebos.com/wenzhen-mini-app/voicegreen.gif');
        background-repeat: no-repeat;
        background-size: cover;
        width: 100%;
        height: 100%;
    }
}

.voiceModalMain {
    position: relative;

    .shadowBox {
        position: absolute;
        top: 45px;
        width: 100%;
        height: 45px;
        background: linear-gradient(180deg, #e8e8f2 20%, #fff0 100%);
        z-index: 3;

        .cancelText {
            position: absolute;
            left: 30%;
            background-color: #fa999f;
            border: 1px solid #fa999f;
            border-radius: 81px;
            font-size: 42px;
            color: #fff;
        }
    }
}

.scrollResult {
    height: 519px;

    .textCenter {
        text-align: center;
    }

    .textLeft {
        text-align: left;
    }
}

.voiceTextarea {
    width: 90%;
    line-height: 99px;
    font-size: 63px;
    position: relative;
}
