import {useState, useRef, useCallback} from 'react';

import {getRecorderManager, type RecorderManager} from '@tarojs/taro';
import {showToast} from '../../../../../../../../utils/customShowToast';

import GetVoiceRecognizer from './asr/index';
import type {IAsrError} from './asr/index.d';

export type VoiceStatus = 'INIT' | 'ERROR' | 'START' | 'RECOGNIZING' | 'RECOGNIZING_STOP' | 'STOP';

export type VoiceFile = {duration: number; fileSize: number; tempFilePath: string};

export interface Voice {
    handleVoiceStart: () => void;
    handleVoiceEnd: (e?: {reset?: boolean}) => Promise<void>;
    handleVoiceChange: (e?: VoiceChangeRes) => void;
    voiceResult: string;
    voiceStatus: VoiceStatus;
}

export interface VoiceChangeRes {
    status: VoiceStatus;
    value: string;
}

export interface VoiceTokenInit {
    secretkey: string;
    secretid: string;
    token: string;
}

export default ({aiVoiceToken}) => {
    const voiceManager = useRef(new GetVoiceRecognizer(aiVoiceToken));
    const recorderManager = useRef<RecorderManager>(getRecorderManager());
    const [voiceResult, setVoiceResult] = useState<string>('');
    const [voiceStatus, setVoiceStatus] = useState<VoiceStatus>('INIT');

    const voiceFile = useRef<VoiceFile>({duration: 0, fileSize: 0, tempFilePath: ''});
    const voiceErr = useRef<IAsrError | null>(null);
    // if (process.env.TARO_ENV === 'weapp') {
    //     const audioCtx = createWebAudioContext();

    //     const analyser = audioCtx?.createAnalyser();

    //     recorderManager.current.onFrameRecorded(listener => {
    //         if (listener.isLastFrame) {

    //             console.log('soundIntensity', 0);

    //         } else {
    //             audioCtx.decodeAudioData(listener.frameBuffer, buffer => {
    //                 const source = audioCtx.createBufferSource();
    //                 source.buffer = buffer;
    //                 source.connect(analyser);

    //                 source.start();

    //                 const n = new Uint8Array(analyser.frequencyBinCount);

    //                 analyser.getByteTimeDomainData(n);

    //                 let i = 0; let r = 0; let s = 0;

    //                 r = Math.max.apply(null, n);

    //                 s = Math.min.apply(null, n);

    //                 i = (r - s) / 128;

    //                 i = Math.round(i * 100 / 2);

    //                 i = i > 100 ? 100 : i;

    //                 console.log('soundIntensity', listener.isLastFrame ? 0 : i);

    //             }, err => {

    //                 console.error('decodeAudioData fail', err);

    //             }
    //         );

    //         }
    //     });
    // }

    // 开始录音
    const voiceStart = useCallback(async (res?: VoiceTokenInit) => {
        setVoiceStatus('INIT');
        voiceManager.current.start(res);
    }, []);

    // 停止录音
    const voiceStop = useCallback((callBack?: () => void) => {
        voiceManager.current.stop();
        callBack && callBack();
    }, []);

    // 识别开始回调
    voiceManager.current.onStart = () => {
        setVoiceStatus('START');
    };

    // 识别语音变化回调
    voiceManager.current.onRecognize = res => {
        const _result = res?.result || '';
        _result && setVoiceResult(_result);
        setVoiceStatus('RECOGNIZING');
    };

    // 一句话识别完成
    voiceManager.current.onRecognizeStop = res => {
        const _result = res?.result || '';
        _result && setVoiceResult(_result);
        // setVoiceStatus('RECOGNIZING_STOP');
    };

    // 识别完成回调
    // voiceManager.current.onStop = res => {
    // };

    // // 录音停止回调
    // voiceManager.current.onRecorderStop = res => {
    // };

    voiceManager.current.onError = async err => {
        switch (err.errHandle) {
            case 'igorne':
                return;
            case 'reStart':
                return;
            case 'getMic':
                break;
            default:
                break;
        }
        err.errToast &&
            showToast({
                title: err.errToast,
                icon: 'none'
            });
        voiceErr.current = err;

        setVoiceStatus('ERROR');
    };

    // 录音事件-监听停止
    recorderManager.current.onStop(res => {
        // 打印中使用了参数getLidState，保留
        voiceFile.current = res;
        setVoiceStatus('STOP');
        setVoiceResult('');
        // onStopCallBack && onStopCallBack(res, voiceResult);
    });

    // 录音事件-监听录音打断开始
    recorderManager.current.onInterruptionBegin(_res => {
        // onInterrupCallback && onInterrupCallback(res, voiceResult);
    });

    // 录音事件-监听录音打断停止
    // recorderManager.current.onInterruptionEnd(() => {
    // });

    return {
        voiceStart,
        voiceStop,
        setVoiceResult,
        voiceResult,
        voiceFile,
        voiceStatus
    };
};
