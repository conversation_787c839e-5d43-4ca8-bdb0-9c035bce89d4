// Author: z<PERSON><PERSON>yu03
// Date: 2025-02-21 11:13:20
// Description: 胶囊组件
import cx from 'classnames';
import {debounce} from 'lodash-es';
import {WImage} from '@baidu/wz-taro-tools-core';
import {View, Text, Block} from '@tarojs/components';
import {showLoading, hideLoading} from '@tarojs/taro';
import {CLoginButton} from '@baidu/vita-ui-cards-common';
import {memo, useCallback, useEffect, useMemo, useRef, useState} from 'react';
import {WiseInquiry, WiseCalendarReserve, WiseOrder, WiseLove} from '@baidu/wz-taro-tools-icons';

import {getUseractionReq} from '../../../../../../../models/services/docIm';

import useDailyStorage from '../hook/useDailyStorage';
import {useCapsuleToolsController} from '../../../../../../../hooks/docIm/useCapsuleTools';
// FIXME 这里只迁了一部分，依赖里有一些操作状态的方法还没迁，目前看暂不影响
import {useHandleUserLoginBizAction} from '../../../../../../../hooks/docIm/useHandleUserBizAction';
import {isPaidMed} from '../../../../../../../utils';
import {
    useGetUserData,
    useGetSessionId,
    useUpdateUserData,
    useSetImageSource,
    useGetCapsulesTools,
    useGetAdjectiveDirectedSkuMsgId
} from '../../../../../../../hooks/docIm/pageDataController';
import {useGetUrlParams} from '../../../../../../../hooks/common';
import {useSetTipsData} from '../../../../../../../hooks/docIm/useHandleTipsAction';
import {useMsgDataSetController} from '../../../../../../../hooks/docIm/dataController';
import {useGetSwanMsgListSceneStatus} from '../../../../../../../hooks/docIm/useGetSwanMsgListSceneStatus';

import type {CapsulesToolsType} from '../../../../../../../store/docImAtom/index.type';
import type {SceneTypeOfParams} from '../../../../../../../models/services/docIm/sse/index.d';

import {navigate, type UrlParams} from '../../../../../../../utils/basicAbility/commonNavigate';
import {ubcCommonClkSend, ubcCommonViewSend} from '../../../../../../../utils/generalFunction/ubc';
import {SaveMaterialPopup} from '../../../../../../../components/SaveMaterialPopup';

import type {ActionInfo, Instruction} from '../../../../../../../typings/index.d';
import type {CapsuleToolsProps, BubbleInfo} from './index.d';

import styles from './index.module.less';

const needLoginTools = [
    'reward' // 送心意
];

const CapsuleTools = memo((props: CapsuleToolsProps) => {
    const {openUploadPopup} = props;
    const isPaidMedScene = isPaidMed();

    const isLoading = useRef(false);
    const hasRendered = useRef(false);
    // eslint-disable-next-line no-undef
    const timer = useRef<NodeJS.Timeout | null>(null);

    const {userData} = useGetUserData();
    const isLoginRef = useRef(userData?.isLogin || false);

    const sessionId = useGetSessionId();
    const {expert_id} = useGetUrlParams();
    const {setTipsData} = useSetTipsData();
    const {updateUserData} = useUpdateUserData();
    const {setImageSource} = useSetImageSource();
    const {status} = useGetSwanMsgListSceneStatus();
    const {sessionCapsulesTools = []} = useGetCapsulesTools();
    const {handleLoginBizAction} = useHandleUserLoginBizAction();
    const {adjectiveDirectedSkuMsgId} = useGetAdjectiveDirectedSkuMsgId();
    const {getStoredValue, setValue} = useDailyStorage('capsuleGuide', false);
    const {updateWzMockMessage, updateWzMessage} = useCapsuleToolsController();
    const {updateMsgData} = useMsgDataSetController({msgId: adjectiveDirectedSkuMsgId});

    const [bubbleInfo, setBubbleInfo] = useState({} as BubbleInfo);
    const [hideGuideCard, setHideGuideCard] = useState(getStoredValue());
    const [isLogin, setIsLogin] = useState<boolean>(userData?.isLogin || false);
    const [open, setOpen] = useState(false);
    const [popUpData, setPopUpData] = useState<CapsulesToolsType['instruction']>();

    /**
     * @description 点击工具栏内容
     * @param actionInfo 跳转的链接
     */
    const handleClickItem = useCallback(
        (actionInfo: ActionInfo, type: CapsulesToolsType['type'], instruction?: Instruction) => {
            const {interaction, interactionInfo, intent = ''} = actionInfo;
            if (interaction === 'openLink') {
                navigate({
                    url: interactionInfo?.url,
                    params: interactionInfo?.params as UrlParams,
                    openType: 'navigate'
                });
            } else if (interaction === 'request') {
                updateWzMockMessage(interactionInfo);
            } else if (interaction === 'sendMsg') {
                // 新增发消息的逻辑
                updateWzMessage(
                    interactionInfo,
                    {
                        sceneType: interactionInfo?.sceneType as SceneTypeOfParams
                    },
                    intent
                );
            } else if (interaction === 'login') {
                handleLoginBizAction();
            } else if (interaction === 'sendImg') {
                openUploadPopup(interactionInfo?.sceneType as SceneTypeOfParams);
                instruction && setTipsData(instruction as CapsulesToolsType['instruction']);
                setImageSource(`capsules_${type}`);
            } else if (interaction === 'popup') {
                if (instruction && interactionInfo.sceneType === 'consumerHealthcare') {
                    setOpen(true);
                    instruction && setPopUpData(instruction as CapsulesToolsType['instruction']);
                }
            }

            ubcCommonClkSend({
                value: `capsules_${type}`
            });
        },
        [
            handleLoginBizAction,
            openUploadPopup,
            setImageSource,
            setTipsData,
            updateWzMessage,
            updateWzMockMessage
        ]
    );

    // 胶囊工具根据是否是手百中心进入处理
    const transCapsuleClass = useMemo(() => {
        if (status) {
            return 'wz-ptb-21';
        }

        return 'wz-ptb-30';
    }, [status]);

    // 胶囊工具Icon根据是否是手百中心进入处理

    const transCapsuleIconClass = useMemo(() => {
        if (status) {
            return styles.swanMsgCapsuleIcon;
        }

        return styles.capsuleIcon;
    }, [status]);

    // 胶囊工具间距根据是否是手百中心进入处理
    const transCapsuleMarClass = useMemo(() => {
        if (status) {
            return 'wz-mr-36';
        }

        return 'wz-mr-24';
    }, [status]);

    // 胶囊工具文字根据是否是手百中心进入处理
    const transCapsuleTextClass = useMemo(() => {
        if (status) {
            return styles.swanMsgToolsText;
        }

        return styles.toolsText;
    }, [status]);

    const transCapsuleBrClass = useMemo(() => {
        if (status) {
            return styles.swanMsgBorderCapsule;
        }

        return styles.borderCapsule;
    }, [status]);

    const iconMap = useCallback(
        icon => {
            const iconMap = {
                'wise-inquiry': <WiseInquiry size={48} color='#00C8C8' />,
                'wise-calendarReserve': <WiseCalendarReserve size={48} color='#00B5F2' />,
                'wise-order': <WiseOrder size={48} color='#00c8c8' />,
                'wise-love': <WiseLove size={48} color='#FD503E' />
            };

            if (icon?.startsWith('https')) {
                return <WImage src={icon} className={transCapsuleIconClass} />;
            } else {
                return iconMap[icon];
            }
        },
        [transCapsuleIconClass]
    );

    const renderGuide = useCallback(() => {
        if (hideGuideCard) {
            return;
        }

        if (!bubbleInfo || !Object.keys(bubbleInfo)?.length) {
            return;
        }

        const {icon, showTime = 5000, text} = bubbleInfo;

        timer.current = setTimeout(() => {
            setHideGuideCard(true);
            setValue(true);
        }, showTime);

        return (
            <View
                className={cx(styles.capsuleGuide, 'wz-flex', 'wz-pr-45')}
                onClick={() => {
                    setHideGuideCard(true);
                    setValue(true);
                }}
            >
                <WImage src={icon} className={styles.capsuleGuideIcon} />
                <View className={styles.capsuleGuideText}>{text}</View>
            </View>
        );
    }, [bubbleInfo, hideGuideCard, setValue]);

    const onLoginSuccess = useCallback(
        async ({actionInfo, type, instruction}) => {
            if (isLoading.current) return;

            if (sessionId && !isLogin && !isLoginRef.current) {
                isLoading.current = true;
                const params = {
                    bizActionType: 'userLogin' as const,
                    chatData: {
                        sessionId,
                        expertId: Number(expert_id || '')
                    },
                    bizActionData: {
                        userLoginInfo: {
                            msgId: adjectiveDirectedSkuMsgId || ''
                        }
                    }
                };
                showLoading({
                    title: '登录中...',
                    mask: true
                });
                const [err, data] = await getUseractionReq<'userLogin'>(params);
                hideLoading();
                isLoading.current = false;

                if (!err) {
                    setIsLogin(data?.data?.userData?.isLogin || false);
                    isLoginRef.current = data?.data?.userData?.isLogin || false;

                    data?.data?.userData && updateUserData(data?.data?.userData);
                    data?.data?.message[0] && updateMsgData(data?.data?.message[0]);

                    handleClickItem(actionInfo, type, instruction);
                }

                return;
            }

            handleClickItem(actionInfo, type, instruction);
        },
        [
            adjectiveDirectedSkuMsgId,
            expert_id,
            handleClickItem,
            isLogin,
            sessionId,
            updateMsgData,
            updateUserData
        ]
    );

    const genCon = useMemo(() => {
        if (!sessionCapsulesTools || !sessionCapsulesTools?.length) return null;

        if (!hasRendered.current) {
            hasRendered.current = true;

            sessionCapsulesTools.forEach(item => {
                ubcCommonViewSend({
                    value: `capsules_${item?.type}`
                });
            });
        }
        return sessionCapsulesTools?.map((item, index) => (
            <Block key={index}>
                {needLoginTools.includes(item?.type) &&
                !userData?.isLogin &&
                !isLoginRef.current ? (
                        <CLoginButton
                            isLogin={userData?.isLogin || isLoginRef.current}
                            closeShowNewUserTag={true}
                            useH5CodeLogin={true}
                            onLoginFail={error => {
                                console.error('error', error);
                            }}
                            onLoginSuccess={async () => {
                                await onLoginSuccess({
                                    actionInfo: item?.actionInfo as ActionInfo,
                                    type: item?.type,
                                    instruction: item?.instruction
                                });
                            }}
                            className={cx(styles.cLoginButton, 'wz-flex')}
                            key={index}
                        >
                            <View
                                className={cx(
                                    styles.toolsItem,
                                    transCapsuleMarClass,
                                    transCapsuleBrClass,
                                    'wz-flex',
                                    'wz-mb-15',
                                    'wz-plr-36',
                                    // 'wz-ptb-36',
                                    'c-click-status',
                                    transCapsuleClass,
                                    index === 0 ? 'wz-ml-36' : ''
                                )}
                                onTouchMove={() => {
                                    setHideGuideCard(true);
                                    setValue(true);
                                }}
                            >
                                {iconMap(item.icon)}
                                <Text className={cx(transCapsuleTextClass, 'wz-ml-15')}>
                                    {item?.text}
                                </Text>
                            </View>
                        </CLoginButton>
                    ) : (
                        <View
                            className={cx(
                                styles.toolsItem,
                                transCapsuleMarClass,
                                transCapsuleBrClass,
                                'wz-flex',
                                'wz-mb-15',
                                'wz-plr-36',
                                // 'wz-ptb-36',
                                'c-click-status',
                                transCapsuleClass,
                                index === 0 ? 'wz-ml-36' : ''
                            )}
                            key={index}
                            onTouchMove={() => {
                                setHideGuideCard(true);
                                setValue(true);
                            }}
                            onClick={debounce(
                                () =>
                                    handleClickItem(
                                    item?.actionInfo as ActionInfo,
                                    item?.type,
                                    item?.instruction
                                    ),
                                500
                            )}
                        >
                            {iconMap(item.icon)}
                            <Text className={cx(transCapsuleTextClass, 'wz-ml-15')}>{item?.text}</Text>
                        </View>
                    )}
            </Block>
        ));
    }, [
        iconMap,
        setValue,
        onLoginSuccess,
        handleClickItem,
        userData?.isLogin,
        sessionCapsulesTools,
        transCapsuleClass,
        transCapsuleTextClass,
        transCapsuleBrClass,
        transCapsuleMarClass
    ]);

    useEffect(() => {
        try {
            if (hasRendered.current) {
                const bubble = sessionCapsulesTools?.[0]?.bubbleInfo;
                if (bubble) {
                    setBubbleInfo(bubble);
                } else {
                    setHideGuideCard(true);
                }
            }
        } catch (error) {
            console.error(error, 'error');
        }
    }, [sessionCapsulesTools]);

    useEffect(() => {
        if (hideGuideCard) {
            // eslint-disable-next-line @typescript-eslint/ban-ts-comment
            //@ts-expect-error
            clearTimeout(timer.current);
        }
    }, [hideGuideCard]);

    return (
        <View
            className={cx(
                styles.capsuleToolsCom,
                isPaidMedScene ? styles.capsuleToolsComPaidMed : '',
                'wz-flex'
            )}
        >
            {hideGuideCard ? <View className={styles.capsuleShadow} /> : ''}
            {renderGuide()}
            <View className={cx(styles.toolsItemList, 'wz-flex')}>{genCon}</View>
            {
                isPaidMedScene && <SaveMaterialPopup
                    open={open}
                    handleClose={() => setOpen(false)}
                    saveMaterialPopupContent={popUpData}
                />
            }
        </View>
    );
});

CapsuleTools.displayName = 'CapsuleTools';

export default CapsuleTools;
