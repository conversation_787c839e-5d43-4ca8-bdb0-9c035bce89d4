.capsuleToolsCom {
    justify-content: space-between;
    background-color: #eff3f9;
    scrollbar-width: none;
    scrollbar-color: transparent transparent;
    position: relative;

    &PaidMed {
        background: #e8f0ff;

        .capsuleShadow {
            background: linear-gradient(180deg, rgb(232 240 255 / 0%) 0%, #e8f0ff 100%) !important;
        }
    }

    .capsuleGuide {
        position: absolute;
        top: -190px;
        left: 50px;
        height: 135px;
        background: linear-gradient(10deg, #00cfa3 0%, #00d3ea 100%);
        border-radius: 45px;

        .capsuleGuideIcon {
            width: 168px;
            height: 216px;
            animation: guide-animation 1.5s infinite;
        }

        .capsuleGuideText {
            white-space: nowrap;
            color: #fff;
        }
    }

    @keyframes guide-animation {
        0%,
        100% {
            transform: translateY(0);
            animation-timing-function: cubic-bezier(0.8, 0, 1, 1);
        }

        50% {
            transform: translateY(-20px);
            animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
        }
    }

    .capsuleShadow {
        position: absolute;
        width: 100%;
        height: 136px;
        background: linear-gradient(180deg, rgb(239 243 249 / 0%) 0%, #eff3f9 100%);
        z-index: 10;
        top: -136px;
        left: 0;
    }

    .capsuleIcon {
        width: 54px;
        height: 54px;
    }

    .swanMsgCapsuleIcon {
        width: 42px;
        height: 42px;
    }

    /* 隐藏滚动条，在 Webkit 浏览器中 */
    &::-webkit-scrollbar {
        width: 0;
        height: 0;
    }

    .toolsItem {
        background: #fff;
        overflow: visible;

        .toolsText {
            font-size: 42px;
            white-space: nowrap;
        }

        .swanMsgToolsText {
            font-size: 39px;
            white-space: nowrap;
        }
    }

    .borderCapsule {
        border-radius: 45px;
    }

    .swanMsgBorderCapsule {
        border-radius: 60px;
    }

    .loginItem {
        width: 100%;
        border-radius: 36px;
        border: solid 3px #f3f4f5;
        color: #fff;
        background-image: url('https://med-fe.cdn.bcebos.com/wz%2FguideLogin.png');
        background-size: cover;
    }

    .loginTips {
        flex-shrink: 0;
    }

    .loginBtn {
        background: #fff;
        border-radius: 45px;
        color: #00bdbd;
        font-weight: 600;
    }

    .cLoginButton {
        width: 100%;
    }

    .toolsItemList {
        width: 100%;
        box-sizing: border-box;
        position: relative;
        overflow-x: scroll;

        &::-webkit-scrollbar {
            display: none;
        }
    }

    .noGuideCard {
        overflow: scroll;
    }

    .orderInfoList {
        width: 180px;
        position: relative;

        &::before {
            content: '';
            position: absolute;
            width: 20px;
            height: 100%;
            left: -20px;
            background: linear-gradient(to left, #f3f4f5, #f3f4f500);
        }
    }
}
