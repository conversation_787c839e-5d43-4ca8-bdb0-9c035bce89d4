.serviceStatementContainer {
    width: 100%;
    padding: 30px 45px;
    box-sizing: border-box;
    font-family: PingFang SC;
    font-weight: 400;
    color: #b7b9c1;
    flex-wrap: wrap;
    align-items: center;
    background-color: #eff3f9;

    .serviceStatementItemContainer {
        flex: 1;
        flex-wrap: wrap;
        align-items: center;
        white-space: pre-wrap;
        justify-content: center;

        .highlightItem {
            color: #00c8c8;
            line-height: 1.4 !important;
        }

        .highlightItemPaidMed {
            color: #4d69f0;
            line-height: 1.4 !important;
        }

        .item {
            color: #b7b9c1;
            line-height: 1.4 !important;
        }
    }
}
