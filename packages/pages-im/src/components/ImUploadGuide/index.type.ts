export const UPLOAD_STYLE = {
    camera: '拍照',
    album: '上传照片'
} as const;

export const UPLOAD_ICON = {
    camera: 'https://med-fe.cdn.bcebos.com/vita/vita_usua_camera.png',
    album: 'https://med-fe.cdn.bcebos.com/vita/vita_uploadSolid.png'
} as const;

export const UPLOAD_BTN = {
    camera: '去拍照',
    album: '去上传'
} as const;

export interface UploadItemProps {
    sectionId?: string;
    type?: 'text' | 'images';
    content?: string;
}

export interface ImUploadGuideProps {
    // 数据
    data: CardsData<{
        list?: UploadItemProps[];
        instruction: {
            content: Array<{
                type: string;
                data: {
                    value: string;
                    list?: Array<{
                        small?: string;
                    }>;
                };
            }>;
            title: string;
        };
        instructionType: string;
        userActionSnapshot: {
            sceneType: string;
        };
        uploadImgAllowNum: number;
    }>;
    msgId?: string;
}

export interface CardsData<T> {
    cardStyle?: CardStyle;
    content?: T;
    ext?: {
        [key: string]: string | number;
    };
}

export interface CardStyle {
    needHead?: boolean;
    renderType?: number;
    width?: number;
    height?: number;
    positionType?: number;
    isHidden?: boolean;
    disablePreview?: boolean;
}
