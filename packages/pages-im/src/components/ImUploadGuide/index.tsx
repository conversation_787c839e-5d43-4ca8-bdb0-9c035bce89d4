import {View} from '@tarojs/components';
import {previewImage, eventCenter} from '@tarojs/taro';
import cx from 'classnames';
import {useCallback, useMemo} from 'react';
import {WImage} from '@baidu/wz-taro-tools-core';

import {UPLOAD_STYLE, UPLOAD_ICON, UPLOAD_BTN, type ImUploadGuideProps} from './index.type';

import uploadStyles from './index.module.less';

const uploadStyle = ['camera', 'album'];

const ImUploadGuide = (props: ImUploadGuideProps) => {
    const {content} = props?.data || {};
    const {instructionType, instruction, userActionSnapshot, uploadImgAllowNum} = content || {};

    const {content: renderList} = instruction || {};

    const handlePreview = useCallback(url => {
        previewImage({
            current: url,
            urls: [url]
        });
    }, []);

    const handleUpload = useCallback(
        btnType => {
            eventCenter.trigger('handleUpload', {
                sceneType: userActionSnapshot?.sceneType || instructionType,
                btnType,
                count: uploadImgAllowNum
            });
        },
        [instructionType, userActionSnapshot]
    );

    const memoRenderTitle = useMemo(() => {
        const titleList = renderList?.find(item => item?.type === 'title');

        if (!titleList) return;

        return <View className={uploadStyles.uploadTitle}>{titleList?.data?.value}</View>;
    }, [renderList]);

    const memoRenderText = useMemo(() => {
        const textList = renderList?.find(item => item?.type === 'text');
        if (!textList) return;

        return <View className={uploadStyles.uploadText}>{textList?.data?.value}</View>;
    }, [renderList]);

    const memoRenderImg = useMemo(() => {
        const imgList = renderList?.find(item => item?.type === 'img');
        if (!imgList) return;

        return (
            <View className={cx('wz-mt-63', 'wz-flex')}>
                {imgList?.data?.list?.map((item, idx) => (
                    <WImage
                        key={idx}
                        className={uploadStyles.uploadImg}
                        shape='rounded'
                        src={item.small}
                        onClick={() => handlePreview(item.small)}
                    />
                ))}
            </View>
        );
    }, [renderList, handlePreview]);

    const renderUploadMode = useMemo(() => {
        return (
            <View className={cx(uploadStyles.uploadMode, 'wz-mt-54', 'wz-plr-63')}>
                {uploadStyle.map((item, idx) => {
                    return (
                        <View
                            key={idx}
                            className={cx(uploadStyles.modeItem, 'wz-flex', 'wz-ptb-63')}
                        >
                            <View className={cx(uploadStyles.modeTitle, 'wz-flex')}>
                                <WImage
                                    className={uploadStyles.uploadIcon}
                                    src={UPLOAD_ICON[item]}
                                />
                                <View className={cx(uploadStyles.modeText, 'wz-ml-36')}>
                                    {UPLOAD_STYLE[item]}
                                </View>
                            </View>
                            <View
                                className={cx(uploadStyles.modeBtn, 'wz-flex')}
                                onClick={() => handleUpload(item)}
                            >
                                {UPLOAD_BTN[item]}
                            </View>
                        </View>
                    );
                })}
            </View>
        );
    }, [handleUpload]);

    if (!content) return;

    return (
        <View className={uploadStyles.uploadMain}>
            <View className={cx(uploadStyles.bubbleWrapperUpload, 'wz-plr-42', 'wz-ptb-45')}>
                {memoRenderTitle}
                {memoRenderText}
                {memoRenderImg}
            </View>
            {renderUploadMode}
        </View>
    );
};

ImUploadGuide.displayName = 'ImUploadGuide';

export default ImUploadGuide;
