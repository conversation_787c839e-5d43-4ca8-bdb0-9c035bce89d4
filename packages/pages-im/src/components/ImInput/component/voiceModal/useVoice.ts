import {useState, useRef, useCallback} from 'react';

import {vibrateShort, nextTick} from '@tarojs/taro';
import {showToast} from '../../../../utils/customShowToast';
import {ubcCommonClkSend} from '../../../../utils/generalFunction/ubc';
import {useGetSessionId} from '../../../../hooks/triageStream/pageDataController';

import GetVoiceRecognizer from './asr/index';
import {IAsrError} from './asr/index.d';

export type VoiceStatus = 'INIT' | 'ERROR' | 'START' | 'RECOGNIZING' | 'RECOGNIZING_STOP' | 'STOP';

export type VoiceFile = {duration: number; fileSize: number; tempFilePath: string};

export interface Voice {
    handleVoiceStart: () => void;
    handleVoiceEnd: (e?: {reset?: boolean}) => Promise<void>;
    handleVoiceChange: (e?: VoiceChangeRes) => void;
    voiceResult: string;
    voiceStatus: VoiceStatus;
}

export interface VoiceChangeRes {
    status: VoiceStatus;
    value: string;
}

export interface VoiceTokenInit {
    secretkey: string;
    secretid: string;
    token: string;
}

export default ({aiVoiceToken}) => {
    const curSessionId = useGetSessionId();
    const voiceManager = useRef(new GetVoiceRecognizer(aiVoiceToken));
    // const recorderManager = useRef<RecorderManager>(getRecorderManager());
    const [voiceResult, setVoiceResult] = useState<string>('');
    const [voiceStatus, setVoiceStatus] = useState<VoiceStatus>('INIT');

    const voiceFile = useRef<VoiceFile>({duration: 0, fileSize: 0, tempFilePath: ''});
    const voiceErr = useRef<IAsrError | null>(null);

    // 开始录音
    const voiceStart = useCallback(async (res?: VoiceTokenInit) => {
        vibrateShort();
        nextTick(() => {
            setVoiceStatus('INIT');
            voiceManager.current.start(res);
        });
    }, []);

    // 停止录音
    const voiceStop = useCallback((callBack?: () => void) => {
        voiceManager.current.stop();
        callBack && callBack();
        nextTick(() => {
            setVoiceStatus('STOP');
            setVoiceResult('');
            vibrateShort();
        });
    }, []);

    // 识别开始回调
    voiceManager.current.onStart = () => {
        setVoiceStatus('START');
    };

    // 识别语音变化回调
    voiceManager.current.onRecognize = res => {
        const _result = res?.result || '';
        _result && setVoiceResult(_result);
        setVoiceStatus('RECOGNIZING');
    };

    // 一句话识别完成
    voiceManager.current.onRecognizeStop = res => {
        const _result = res?.result || '';
        _result && setVoiceResult(_result);
        // setVoiceStatus('RECOGNIZING_STOP');
    };

    voiceManager.current.onError = async err => {
        // switch (err.errHandle) {
        //     case 'igorne':
        //         return;
        //     case 'reStart':
        //         return;
        //     case 'getMic':
        //         break;
        //     default:
        //         break;
        // }
        err.errToast &&
            showToast({
                title: err.errToast,
                icon: 'none'
            });
        voiceErr.current = err;

        setVoiceStatus('ERROR');

        // 上报语音异常错误
        ubcCommonClkSend({
            value: 'vita_voice_error',
            ext: {
                product_info: {
                    sessionId: curSessionId || '',
                    error: JSON.stringify(err)
                }
            }
        });
    };

    // 录音事件-监听停止
    // recorderManager.current.onStop(res => {
    //     // 打印中使用了参数getLidState，保留
    //     voiceFile.current = res;
    //     setVoiceStatus('STOP');
    //     setVoiceResult('');
    //     vibrateShort();
    //     // onStopCallBack && onStopCallBack(res, voiceResult);
    // });

    // // 录音事件-监听录音打断开始
    // recorderManager.current.onInterruptionBegin(_res => {
    //     // onInterrupCallback && onInterrupCallback(res, voiceResult);
    // });

    // 录音事件-监听录音打断停止
    // recorderManager.current.onInterruptionEnd(() => {
    // });

    return {
        voiceStart,
        voiceStop,
        setVoiceResult,
        voiceResult,
        voiceFile,
        voiceStatus
    };
};
