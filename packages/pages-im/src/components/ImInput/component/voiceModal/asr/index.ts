import {IAsrHandler, IAsrError} from './index.d';

export default class AsrHandler implements IAsrHandler {
    onStart: (res) => void;
    onRecognize: (res) => void;
    onRecognizeStop: (res) => void;
    onError: (err: IAsrError) => Promise<void>;
    onRecorderStop: (res) => void;
    onStop: (res) => void;
    constructor(_options: {[key: string]: string}) {}
    start: (res) => void;
    stop: () => void;
    cancel: () => unknown;
    public canIUse = false;
}
