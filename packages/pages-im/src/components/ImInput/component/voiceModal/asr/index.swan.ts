import {IAsrHandler, IHandlerCallbackNode, IAsrError} from './index.d';
import errorVoiceMap from './errorMap';

// 相关文档：
// https://smartprogram.baidu.com/docs/develop/api/ai/voice_swan-ai-getVoiceRecognizer/
interface IAsr extends IAsrHandler, IHandlerCallbackNode {}

class AsrHandler implements IAsr {
    private voiceRecognizer;
    public canIUse = true;
    private configParams;
    // 多端统一打平对外暴漏的回调
    public onError: IHandlerCallbackNode['onError'];
    public onStart: IHandlerCallbackNode['onStart'];
    public onRecognize: IHandlerCallbackNode['onRecognize'];
    public onRecognizeStop: IHandlerCallbackNode['onRecognizeStop'];
    public onRecorderStop: IHandlerCallbackNode['onRecorderStop'];
    public onStop: IHandlerCallbackNode['onStop'];
    public onCancel: IHandlerCallbackNode['onCancel'];
    private _temText: string;
    constructor() {
        this.voiceRecognizer = swan.ai.getVoiceRecognizer();
        this._temText = '';
    }

    private handleErr = res => {
        const errCode = +res?.errorCode;
        const __err: IAsrError = {errCode};
        switch (errCode) {
            case 3002:
            case 2001:
            case 2008:
                // 手百录音设备异常/冲突/鉴权失败
                __err.errType = 'deviceConflict';
                __err.errHandle = 'reStart';
                break;

            case 4003:
            case 3001:
                // 没有匹配的识别结果、请求参数错误（安卓误报, 临时解决）
                __err.errHandle = 'igorne';
                break;

            case 2004:
            case 2005:
            case 10003:
                __err.errToast = '请检查麦克风、录音权限';
                __err.errType = 'noMic';
                __err.errHandle = 'getMic';
                break;

            default:
                __err.errToast =
                    (errorVoiceMap[errCode] || '录音失败，请尝试使用文字输入') + `(${errCode})`;
                break;
        }

        return {...res, ...__err};
    };

    start() {
        if (!this.canIUse) {
            return;
        }
        this._temText = '';
        this.configParams = {
            mode: 'touch',
            context: 'input',
            longSpeech: true
        };

        // const options: RecorderManager.StartOption = {
        //     duration: 60000,
        //     sampleRate: 16000
        // };

        // 绑定事件回调
        this.voiceRecognizer.onStart(res => {
            this.onStart(res);
        });

        this.voiceRecognizer.onRecognize(res => {
            const _result = res?.result;
            this.onRecognize({result: this._temText + _result});
        });

        this.voiceRecognizer.onFinish(res => {
            const _result = res?.result;
            this._temText = this._temText + _result;
            this.onRecognize({result: this._temText});
        });

        this.voiceRecognizer.onError(res => {
            const err = this.handleErr(res);
            this.onError(err as IAsrError);
        });

        // 开始识别
        this.voiceRecognizer.start(this.configParams);
    }

    stop() {
        this.voiceRecognizer.stop();
    }

    cancel() {
        this.voiceRecognizer.cancel();
        this.onCancel();
    }
}

export default AsrHandler;
