import type {IPicProps} from '../../../../typings/upload';

export type UploadStatus = 'pending' | 'success' | 'failed' | '';

export interface ImageListProps {
    imgList: IPicProps[];
    statusList: UploadStatus[];
    setImgList: (imgList: IPicProps[]) => void;
    setStatusList: (statusList: UploadStatus[]) => void;
    isMultiple: boolean;
    setIsMultiple: (val: boolean) => void;
    bucketConfName: string;
}
