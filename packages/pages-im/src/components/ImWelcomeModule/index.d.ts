import type {InteractionInfo} from '../../typings';
import type {CapsulesToolsType} from '../../store/triageStreamAtom/index.type';

export interface WelcomeContent {
    title: string;
    docImg: string;
    subTitle: string;
    serviceList: ServiceItem[];
    quickReplyTitle?: string;
    quickReply: quickReplyItem[] | undefined;
}

export interface quickReplyItem {
    content: string;
}

export interface ServiceItem {
    type: string;
    icon: string;
    title: string;
    subTitle: string;
    btn: ButtonConfig;
    actionInfo: ActionInfo;
    instruction?: CapsulesToolsType['instruction'];
}

export interface ButtonConfig {
    value: string;
    disabled: boolean;
}

export interface ActionInfo {
    interaction: string;
    interactionInfo: InteractionInfo;
    intent?: string;
}

export interface InteractionParams {
    payload: PayloadItem[];
}

export interface PayloadItem {
    content: string;
    contentType: number;
}
