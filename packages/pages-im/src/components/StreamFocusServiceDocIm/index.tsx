import {View} from '@tarojs/components';
import {showLoading, hideLoading, hideToast} from '@tarojs/taro';
import {memo, type FC, useCallback, useState, useReducer, useRef, useEffect} from 'react';

import {Portal} from '@baidu/vita-ui-cards-common';
import {useGetUrlParams} from '../../hooks/common';

import {
    useGetUserData,
    useGetSessionId,
    useUpdateUserData,
    useGetAdjectiveDirectedSkuMsgId
} from '../../hooks/docIm/pageDataController';
import {useScrollControl} from '../../hooks/common/useScrollControl';
import {useMsgDataSetController} from '../../hooks/docIm/dataController';

import {getUseractionReq} from '../../models/services/docIm';

import {showToast} from '../../utils/customShowToast';
import {ubcCommonViewSend, ubcCommonClkSend} from '../../utils/generalFunction/ubc';

import {getDataForUbcAtom} from '../../store/docImAtom/index';

import PatientCard from './PatientCard';
import ConsultForm from './ConsultForm';
import FocusServiceCard from './FocusServiceCard';
import {formReducer} from './ConsultForm/form-reducer';

import {useGetMakeOrderRelatedInfo} from './hooks/useGetMakeOrderRelatedInfo';

import type {PatientInfo} from './ConsultForm/index.d';
import type {FocusServiceSku} from './FocusServiceCard/index.d';
import type {ImCollectedInfoAndSkuProps, OpenPatientType} from './index.d';

import styles from './index.module.less';

/**
 *
 * @description AI智能体定向服务卡
 * @returns
 */
const StreamFocusService: FC<ImCollectedInfoAndSkuProps> = props => {
    const {content, ext = {}} = props?.data || {};
    const {scene = ''} = ext || {};
    const {collectedInfo, skuData, requireDesc, coupon, isExpired} = content;
    const openPatientPopTypeRef = useRef<boolean>(false); // 存储点开来源类型，用于判断是否发生了变化
    const [showConsultForm, setShowConsultForm] = useState(false); // 是否展示编辑态表单弹层
    const [showConsultList, setShowConsultList] = useState(false); // 是否展示选择就诊人列表弹层
    const [openPatientPopType, setOpenPatientPopType] = useState<OpenPatientType>('patient'); // 点开打开弹窗的来源类型
    const {statusText} = collectedInfo || '';

    const [selectedSkuItemData, setSelectedSkuItemData] = useState<FocusServiceSku>(
        (skuData?.skuList || [])[0]
    );

    const [isSkuDisabled, setIsSkuDisabled] = useState(false);
    const [updateCollectedInfoAndSkuType, setUpdateCollectedInfoAndSku] =
        useState<OpenPatientType>('patient');
    const [selectPatientData, setSelectPatientData] = useState<PatientInfo | undefined>(
        collectedInfo?.curPatient
    );

    const {userData} = useGetUserData();
    const prevIsLoginRef = useRef<boolean>(userData?.isLogin || false); // 存储之前的值，用于判断是否发生了变化

    const sessionId = useGetSessionId();
    const {expert_id} = useGetUrlParams();
    const {updateUserData} = useUpdateUserData();
    const {scrollToMessage} = useScrollControl('docImScrollControl');
    const {adjectiveDirectedSkuMsgId} = useGetAdjectiveDirectedSkuMsgId();
    const {updateMsgData} = useMsgDataSetController({msgId: props?.msgId || ''});
    const [isSkuDetailPopupShow, setIsSkuDetailPopupShow] = useState(false); // 是否展示 sku 详情弹窗
    const [patientFinishTypewriter, setPatientFinishTypewriter] = useState(true); // 打字机是否完成

    useEffect(() => {
        setIsSkuDisabled(isExpired || adjectiveDirectedSkuMsgId !== props?.msgId);
    }, [adjectiveDirectedSkuMsgId, isExpired, props?.msgId]);

    const [state, dispatch] = useReducer(formReducer, {
        ...collectedInfo?.curPatient,
        canSubmit: true,
        telPhone: collectedInfo?.telPhone,
        zhusu: collectedInfo?.clinicalDesc || '',
        images: collectedInfo?.images || []
    });

    // 登录 & 下单部分逻辑
    const {loginSuccessfulCallback, PhoneConsultModalNode, cLoginBtnOps} =
        useGetMakeOrderRelatedInfo({
            selectedSkuItemData,
            msgId: props?.msgId,
            skuList: skuData?.skuList || [],
            ext
        });

    /**
     *
     * @description 更新选中的 skuId，并触发相关计算
     */
    const updateSkuDetailData = useCallback((selectSkuData: FocusServiceSku) => {
        setSelectedSkuItemData(selectSkuData); // 更新选中的 sku 数据
    }, []);

    // 就诊人不存在时，直接展示表单编辑态
    const openEditPatientPop = useCallback(() => {
        setShowConsultList(false);
        setShowConsultForm(true);
        dispatch({
            payload: {
                ...collectedInfo,
                ...collectedInfo?.curPatient,
                zhusu: collectedInfo?.clinicalDesc
            }
        });
    }, [collectedInfo]);

    // 就诊人存在时，打开就诊人选择弹层
    const openEditPatient = useCallback(() => {
        collectedInfo?.patientList?.length && setShowConsultList(true);
        if (collectedInfo?.curPatient?.contactId) {
            setShowConsultList(true);
            setSelectPatientData(
                collectedInfo?.curPatient?.contactId ? collectedInfo?.curPatient : undefined
            );
        }

        dispatch({
            payload: {
                ...collectedInfo,
                ...collectedInfo?.curPatient,
                zhusu: collectedInfo?.clinicalDesc || ''
            }
        });
    }, [collectedInfo]);

    const handleAddPatient = useCallback(() => {
        setShowConsultForm(true);
        setShowConsultList(false);
        setSelectPatientData(undefined);
    }, []);

    // 处理弹层关闭逻辑
    const handleCloseConsultForm = useCallback((type: string) => {
        if (type === 'consult') {
            setShowConsultForm(false);
        }
        if (type === 'consultList') {
            setSelectPatientData(undefined);
            setShowConsultList(false);
        }
    }, []);

    // 选择就诊人
    const handleSelectPatient = useCallback(
        (selectPatient: PatientInfo) => {
            setSelectPatientData(selectPatient);
            dispatch({
                payload: {
                    ...selectPatient,
                    zhusu: state?.zhusu || ''
                }
            });
        },
        [state?.zhusu]
    );

    // 根据当前选中的skuId查找最新的sku数据
    const findSelectedSku = useCallback(
        (skuId: string) => {
            return skuData.skuList?.find(skuItem => skuItem.skuId === skuId);
        },
        [skuData]
    );

    const addPatientToast = useCallback(() => {
        showToast({
            title: '请先填写就诊人信息',
            icon: 'none'
        });
    }, []);

    // 监听登录发生变化后的数据处理
    useEffect(() => {
        if (openPatientPopTypeRef.current) {
            if (openPatientPopType === 'newcomerCoupon') {
                // 如果是点击的是新人券模块，则只走刷新卡片逻辑
                return;
            }
            if (!collectedInfo?.curPatient?.contactId || !collectedInfo?.clinicalDesc) {
                openPatientPopType === 'sku' && addPatientToast();
                if (!collectedInfo?.patientList?.length) {
                    openEditPatientPop();
                } else {
                    openEditPatient();
                }
                setIsSkuDetailPopupShow(false);

                return;
            }
            // 未登录变为登录在状态后，存在就诊人时，点击的为就诊人模块时弹窗就诊人选择列表，否则直接走下单逻辑
            if (updateCollectedInfoAndSkuType === 'patient') {
                openEditPatient();
            } else {
                const selectSku = findSelectedSku(selectedSkuItemData?.skuId || '');
                // 直接走下单逻辑
                selectSku && loginSuccessfulCallback(selectSku);
            }
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [openPatientPopTypeRef.current]);

    const handleSkuDisabled = useCallback(() => {
        showToast({
            title: '请点击最新的服务卡',
            icon: 'none'
        });

        sessionId &&
            adjectiveDirectedSkuMsgId &&
            scrollToMessage(adjectiveDirectedSkuMsgId, 'StreamFocusServiceDocIm/index');

        ubcCommonClkSend({
            value: 'streamFocusService_disable',
            ext: {
                product_info: {
                    ...getDataForUbcAtom()?.product_info,
                    ...ext,
                    msgId: props?.msgId || ''
                }
            }
        });
    }, [adjectiveDirectedSkuMsgId, ext, props?.msgId, scrollToMessage, sessionId]);

    // 更新定向服务卡数据，重新渲染卡片组件
    const updateCollectedInfoAndSku = useCallback(
        async (type: OpenPatientType, selectSku?: FocusServiceSku) => {
            if (isSkuDisabled) {
                handleSkuDisabled();

                return;
            }
            selectSku && setSelectedSkuItemData(selectSku);
            setOpenPatientPopType(type);

            // 未登录(也有可能外部更新了登录状态)
            if (sessionId && !prevIsLoginRef.current && !userData?.isLogin) {
                const params = {
                    bizActionType: 'userLogin' as const,
                    chatData: {
                        sessionId,
                        expertId: Number(expert_id || '')
                    },
                    bizActionData: {
                        userLoginInfo: {
                            msgId: props?.msgId || '',
                            patientInfo: collectedInfo?.curPatient
                        }
                    }
                };
                showLoading({title: '登录中...', mask: true});
                const [err, data] = await getUseractionReq<'userLogin'>(params);
                if (!err) {
                    hideLoading();

                    data?.data?.message[0] && updateMsgData(data?.data?.message[0]);
                    data?.data?.userData && updateUserData(data?.data?.userData);
                    prevIsLoginRef.current = data?.data?.userData?.isLogin || false;

                    if (data?.data?.toast) {
                        hideToast();
                        showToast({
                            title: data?.data?.toast,
                            icon: 'none',
                            duration: 1200,
                            mask: true,
                            complete() {
                                setTimeout(() => {
                                    setUpdateCollectedInfoAndSku(type);
                                    openPatientPopTypeRef.current = true;
                                }, 1200);
                            }
                        });

                        if (type === 'patient') {
                            setUpdateCollectedInfoAndSku(type);
                            openPatientPopTypeRef.current = true;
                        }
                    } else {
                        setUpdateCollectedInfoAndSku(type);
                        openPatientPopTypeRef.current = true;
                    }
                }
            } else {
                // 已登录--点击优惠券--刷新当前sku
                if (type === 'newcomerCoupon') {
                    return;
                }

                // 已登录--未推荐出对应就诊人
                if (!collectedInfo?.curPatient?.contactId) {
                    // 点击【去咨询】时，需要先toast后再出弹层
                    type === 'sku' && addPatientToast();
                    if (
                        (collectedInfo?.curPatient?.age || collectedInfo?.curPatient?.gender) &&
                        !collectedInfo?.patientList?.length
                    ) {
                        openEditPatientPop();
                        return;
                    }

                    openEditPatient();

                    return;
                }

                // 已登录--无就诊人或无病情描述
                if (!collectedInfo?.patientList?.length) {
                    // 点击【去咨询】时，需要先toast后再出弹层
                    type === 'sku' && addPatientToast();
                    openEditPatientPop();
                    return;
                }

                // 已登录--有就诊人--就诊人选择模块
                if (type === 'patient') {
                    openEditPatient();
                } else {
                    // 已登录--有就诊人--服务卡去咨询
                    // 直接走下单逻辑
                    selectSku && loginSuccessfulCallback(selectSku);
                }
            }
            if (type === 'sku') {
                ubcCommonClkSend({
                    value: 'streamFocusService_sku',
                    ext: {
                        value_type: 'sku',
                        value_id: selectSku?.skuId || 'free',
                        product_info: {
                            msgId: props?.msgId || '',
                            ...ext
                        }
                    }
                });
            } else {
                ubcCommonClkSend({
                    value: 'streamFocusService_edit',
                    ext: {
                        product_info: {
                            ...getDataForUbcAtom()?.product_info,
                            ...ext,
                            msgId: props?.msgId || ''
                        }
                    }
                });
            }
        },
        [
            addPatientToast,
            handleSkuDisabled,
            collectedInfo?.clinicalDesc,
            collectedInfo?.curPatient,
            expert_id,
            isSkuDisabled,
            collectedInfo?.patientList?.length,
            loginSuccessfulCallback,
            openEditPatient,
            openEditPatientPop,
            userData?.isLogin,
            props?.msgId,
            sessionId,
            updateMsgData,
            updateUserData,
            ext
        ]
    );

    useEffect(() => {
        ubcCommonViewSend({
            value: isSkuDisabled ? 'streamFocusService' : 'streamFocusService_disabled',
            ext: {
                product_info: {
                    ...getDataForUbcAtom()?.product_info,
                    ...ext,
                    msgId: props?.msgId || ''
                }
            }
        });
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [ext, props?.msgId, scene]);
    return (
        <View className={styles.skuContainer}>
            {/* AI重构-就诊人 */}
            <PatientCard
                isLogin={userData?.isLogin || false}
                collectedInfo={collectedInfo}
                statusText={statusText}
                images={collectedInfo?.images || []}
                updateCollectedInfoAndSku={updateCollectedInfoAndSku}
                isSkuDisabled={isSkuDisabled}
                patientFinishTypewriter={patientFinishTypewriter}
                setPatientFinishTypewriter={setPatientFinishTypewriter}
                cLoginBtnOps={cLoginBtnOps}
                paddingBottom={patientFinishTypewriter ? 72 : 0}
                className={styles.gradient}
                handleSkuDisabled={handleSkuDisabled}
            />

            {/* AI重构-定向服务卡*/}
            <FocusServiceCard
                skuData={skuData}
                coupon={coupon}
                updateSkuDetailData={updateSkuDetailData}
                selectedSkuItemData={selectedSkuItemData}
                updateCollectedInfoAndSku={updateCollectedInfoAndSku}
                isLogin={userData?.isLogin}
                isSkuDisabled={isSkuDisabled}
                msgId={props?.msgId || ''}
                sessionId={sessionId || ''}
                expertId={expert_id}
                PhoneConsultModalNode={PhoneConsultModalNode}
                adjectiveDirectedSkuMsgId={adjectiveDirectedSkuMsgId}
                cLoginBtnOps={cLoginBtnOps}
                isSkuDetailPopupShow={isSkuDetailPopupShow}
                ext={ext}
                setIsSkuDetailPopupShow={setIsSkuDetailPopupShow}
            />
            <Portal>
                {collectedInfo && (showConsultForm || showConsultList) && (
                    <ConsultForm
                        showConsultForm={showConsultForm}
                        showConsultList={showConsultList}
                        collectedInfo={collectedInfo}
                        requireDesc={requireDesc}
                        handleAddPatient={handleAddPatient}
                        handleCloseConsultForm={handleCloseConsultForm}
                        formType={selectedSkuItemData.formType}
                        openEditPatient={openEditPatient}
                        state={state}
                        dispatch={dispatch}
                        openPatientPopType={openPatientPopType}
                        handleSelectPatient={handleSelectPatient}
                        selectPatientData={selectPatientData}
                        selectedSkuItemData={selectedSkuItemData}
                        loginSuccessfulCallback={loginSuccessfulCallback}
                        skuList={skuData?.skuList}
                        msgId={props?.msgId || ''}
                        couponId={coupon?.couponInfo?.id}
                        sessionId={sessionId || ''}
                        expertId={expert_id}
                    />
                )}
            </Portal>
        </View>
    );
};

export default memo(StreamFocusService);
