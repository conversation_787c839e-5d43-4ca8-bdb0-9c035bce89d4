import {InteractionInfo, InteractionType} from '@baidu/vita-pages-im/src/typings';

export interface ImDoctorCardProps {
    data: ExpertInfo;
    onBtnClick?: () => void;
    onCardClick?: () => void;
    isDisplayRec?: boolean;
}

export interface pennantItemData {
    name: string;
    count: number;
}

export interface ExpertInfo {
    expertId: string;
    coreId: string;
    docId: string;
    expertName?: string;
    expertPic?: string;
    expertLevel?: string;
    expertHospital?: string;
    expertDepartment?: string;
    goodCommentCount?: number;
    goodCommentList?: pennantItemData[];
    ghCount?: string;
    urlDes?: string;
    freeTag?: string;
    expertGoodAt?: ExpertGoodAt[];
    price?: string;
    pricePre?: string;
    isOnline?: number;
    attributeTag?: AttributeTag[];
    topLabels?: Label[];
    indicatorList?: Indicator[];
    actionInfo?: ActionInfo;
    btnInfo?: ButtonInfo;
    recReason?: RecReason;
    showPortraitTag?: PortraitTag[];
}

export interface ActionInfo {
    interaction: InteractionType;
    interactionInfo: InteractionInfo;
}

export interface ExpertGoodAt {
    type: 'text';
    value: string;
}

export interface AttributeTag {
    text: string;
    key: string;
    color: string;
    borderColor: string;
}

export interface Label {
    text: string;
    color: string;
    bgColor: string;
    borderColor: string;
}

export interface Indicator {
    text: string;
    value: string;
    highLightColor: string;
}

export interface ButtonInfo {
    value: string;
    interaction: InteractionType;
    interactionInfo: InteractionInfo;
}

export interface PortraitTag {
    type: string;
    value: string;
}

export interface RecReason {
    text: string;
    icon: string;
}
