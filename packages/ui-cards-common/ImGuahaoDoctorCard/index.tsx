import {memo, FC} from 'react';
import cx from 'classnames';
import {View} from '@tarojs/components';

import type {ImDoctorCardProps} from './index.d';
import DoctorAvatar from './components/DoctorAvatar';
import DoctorInfo from './components/DoctorInfo';
import styles from './index.module.less';

const ImGuahaoDoctorCard: FC<ImDoctorCardProps> = ({data, onBtnClick, onCardClick}) => {
    return (
        <View className={cx(styles.doctorSkuCardWrapper)}>
            {/* 医生信息 */}
            <View className={cx(styles.doctorCardWrapper)}>
                <View className={cx(styles.doctorCardAvatarWrapper)}>
                    <DoctorAvatar isOnline={data?.isOnline} expertPic={data?.expertPic} />
                </View>
                <View className={cx(styles.doctorCardInfoWrapper, 'wz-ml-27')}>
                    <DoctorInfo
                        expertName={data?.expertName}
                        expertDepartment={data?.expertDepartment}
                        expertHospital={data?.expertHospital}
                        expertLevel={data?.expertLevel}
                        attributeTag={data?.attributeTag}
                        expertGoodAt={data?.expertGoodAt}
                        goodCommentCount={data?.goodCommentCount}
                        goodCommentList={data?.goodCommentList}
                        ghCount={data?.ghCount}
                        urlDes={data?.urlDes}
                        indicatorList={data?.indicatorList}
                        price={data?.price}
                        freeTag={data?.freeTag}
                        pricePre={data?.pricePre}
                        btnInfo={data?.btnInfo}
                        actionInfo={data?.actionInfo}
                        showPortraitTag={data?.showPortraitTag}
                        onBtnClick={onBtnClick}
                        onCardClick={onCardClick}
                    />
                </View>
            </View>
        </View>
    );
};

ImGuahaoDoctorCard.displayName = 'ImGuahaoDoctorCard';
export default memo(ImGuahaoDoctorCard);
