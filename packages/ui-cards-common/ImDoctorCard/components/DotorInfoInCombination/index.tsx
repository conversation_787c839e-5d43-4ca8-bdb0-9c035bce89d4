import cx from 'classnames';
import {memo, useMemo, FC, useCallback, ReactNode} from 'react';
import {isEmpty} from '@baidu/vita-pages-im/src/utils';
import {View, Text} from '@tarojs/components';
import {Tag} from '@baidu/wz-taro-tools-core';
import {pxTransform} from '@tarojs/taro';
import {HButton} from '@baidu/health-ui';
import {formatPrice} from '@baidu/vita-pages-im/src/utils/generalFunction/price';

import Price from '../Price';
import {generateHighlightText} from '../../utils/generateHighlightText';
import SanjiaTag from '../SanjiaTag';

import type {DoctorInfoProps, ExpertGoodAt} from '../DoctorInfo/index.d';
import FuDanInfo from '../FuDanInfo';
import styles from './index.module.less';

const DoctorInfoInZH: FC<DoctorInfoProps> = ({
    expertName,
    expertDepartment,
    expertLevel,
    expertHospital,
    attributeTag,
    expertGoodAt,
    indicatorList,
    price,
    btnInfo,
    fuDanInfo = [],
    showPortraitTag,
    hidePriceBtn,
    btnChildren,
    onBtnClick,
    onCardClick
}) => {
    // 金额转化
    const finalPrice = formatPrice(Number(price), {significant: 1, safeMode: true});

    const showFuDanInfo = fuDanInfo?.length > 0;

    // 渲染三甲、本地等标签
    const renderAttributeTagList = useMemo(() => {
        return (
            attributeTag &&
            attributeTag?.length > 0 &&
            attributeTag.map((i, idx) => {
                return i.key && i.key === 'hospitalLevel' ? (
                    <View key={idx} className={cx('wz-ml-12')}>
                        <SanjiaTag
                            content={i.text}
                            bgColor='#EBF7EF'
                            color='#39B362'
                            variant='contained'
                        />
                    </View>
                ) : (
                    <Tag
                        key={idx}
                        size='medium'
                        shape='square'
                        variant='outlined'
                        style={{
                            padding: `${pxTransform(9)}
                                ${pxTransform(14)} ${pxTransform(8)}`,
                            fontSize: pxTransform(33),
                            backgroundColor: 'inherit',
                            color: i.color || '#FF6600',
                            borderColor: i.borderColor || 'rgba(255,102,0,0.50)',
                            fontWeight: 'bold'
                        }}
                        className={cx('wz-ml-12', styles.tagWrapper)}
                    >
                        <View className={styles.tagTxt}>{i.text}</View>
                    </Tag>
                );
            })
        );
    }, [attributeTag]);

    const genComponent = useCallback((type: string, itemData: ExpertGoodAt) => {
        const componentsMap: {
            [k in string]: ReactNode;
        } = {
            text: (
                /* bca-disable */
                <View
                    className={cx(styles.expertGoodText, 'wz-taro-ellipsis')}
                    dangerouslySetInnerHTML={{
                        __html: generateHighlightText(itemData?.value || '', '#FD503E')
                    }}
                />
            ),
            highLightText: (
                <Text className={cx(styles.expertGoodHighLightText, 'wz-fw-500')}>
                    {itemData?.value}
                </Text>
            )
        };

        return componentsMap[type] || null;
    }, []);

    // 文本展示
    const genContext = useCallback(() => {
        if (expertGoodAt) {
            return (
                !isEmpty(expertGoodAt) &&
                expertGoodAt?.map(i => {
                    return (
                        <View className={styles.expertGoodContentItem} key={i?.value}>
                            {genComponent(i?.type || '', i || '')}
                        </View>
                    );
                })
            );
        }
    }, [expertGoodAt, genComponent]);

    return (
        <View
            className={cx(styles.docInfoWrapper)}
            onClick={e => {
                e.stopPropagation();
                onCardClick?.();
            }}
        >
            <View className={cx(styles.docInfoFlexWrapper, 'wz-flex, wz-row-between, wz-mb-18')}>
                <View className={cx(styles.docInfoFlexLeftWrapper, 'wz-taro-ellipsis')}>
                    {/* 医生姓名、等级、科室 */}
                    <View className={cx(styles.docNameWrapper, 'wz-flex, wz-col-center, wz-mb-21')}>
                        <View className={cx(styles.name, 'wz-mr-18, wz-fw-500')}>
                            {expertName && expertName?.length > 5
                                ? `${expertName.slice(0, 4)}...`
                                : expertName}
                        </View>
                        <View className={cx(styles.line, 'wz-mr-18, wz-fw-400, wz-fs-42')}>
                            {expertLevel}
                        </View>
                        <View
                            className={cx(
                                styles.department,
                                'wz-mr-18, wz-fw-400, wz-fs-42, wz-taro-ellipsis'
                            )}
                        >
                            {expertDepartment}
                        </View>

                        {showPortraitTag?.map(item => (
                            <View
                                key={item.value}
                                className={cx(
                                    styles.reAsked,
                                    'wz-flex',
                                    'wz-col-center',
                                    'wz-fs-33'
                                )}
                            >
                                {item.value}
                            </View>
                        ))}
                    </View>

                    {/* 医院信息 */}
                    <View className={cx(styles.hosInfoWrapper, 'wz-flex, wz-mb-21')}>
                        {/* 医院名称 */}
                        <View
                            className={cx(styles.hosName, 'wz-fs-42, wz-fw-400, wz-taro-ellipsis')}
                        >
                            {expertHospital}
                        </View>
                        {!isEmpty(attributeTag) && (
                            <View className={cx(styles.tagListWrapper, 'wz-flex')}>
                                {renderAttributeTagList}
                            </View>
                        )}
                    </View>

                    {/* 咨询量 */}
                    {!showFuDanInfo && (
                        <View className={cx('wz-flex', styles.tips)}>
                            {Array.isArray(indicatorList) &&
                                indicatorList?.map((item, index) => (
                                    <View key={index} className={cx('wz-flex')}>
                                        {item?.text}
                                        <Text
                                            className={cx(
                                                item?.value === '暂无' ? styles.gray : styles.blue,
                                                styles.serviceVal,
                                                'wz-fw-500'
                                            )}
                                        >
                                            {item?.value}
                                        </Text>
                                        {indicatorList && indicatorList?.length > index + 1 && (
                                            <View className={cx(styles.tipsLine)} />
                                        )}
                                    </View>
                                ))}
                        </View>
                    )}

                    {showFuDanInfo && (
                        <View>
                            <FuDanInfo fuDanList={fuDanInfo} />
                        </View>
                    )}
                </View>

                {/* 价格 & 次数 */}
                {!hidePriceBtn && (
                    <View className={cx(styles.priceServiceWrapper, 'wz-flex')}>
                        <View className='wz-flex, wz-mb-18'>
                            {/* 无优惠时展示价格 */}
                            {!isEmpty(price) && (
                                <View className={cx(styles.priceWrapper, 'wz-flex, wz-col-bottom')}>
                                    <Text className={cx(styles.mark, 'wz-fw-500, wz-fs-36')}>
                                        ￥
                                    </Text>
                                    <View className={cx(styles.priceNum, 'wz-fs-48, wz-fw-700')}>
                                        <Price price={finalPrice} />
                                    </View>
                                </View>
                            )}
                        </View>
                        {btnInfo && (
                            <View className={cx(styles.btnWrapper)}>
                                <HButton
                                    className='wz-fw-500'
                                    text={btnInfo?.value || '去咨询'}
                                    size={42}
                                    width={198}
                                    height={96}
                                    padding='0 0'
                                    bgColor='linear-gradient(115.5deg, #00CFA3 0%, #00D3EA 102.87%)'
                                    onClick={e => {
                                        e.stopPropagation();
                                        onBtnClick?.();
                                    }}
                                />
                            </View>
                        )}
                    </View>
                )}
            </View>

            {/* 有复旦榜时咨询量在外层 */}
            {showFuDanInfo && (
                <View className={cx('wz-flex, wz-mb-24', styles.tips)}>
                    {Array.isArray(indicatorList) &&
                        indicatorList?.map((item, index) => (
                            <View key={index} className={cx('wz-flex')}>
                                {item?.text}
                                <Text
                                    className={cx(
                                        item?.value === '暂无' ? styles.gray : styles.blue,
                                        styles.serviceVal,
                                        'wz-fw-500'
                                    )}
                                >
                                    {item?.value}
                                </Text>
                                {indicatorList && indicatorList?.length > index + 1 && (
                                    <View className={cx(styles.tipsLine)} />
                                )}
                            </View>
                        ))}
                </View>
            )}

            {/* 擅长 */}
            {expertGoodAt && expertGoodAt?.length > 0 && (
                <View className={cx(styles.goodAtWrapper, 'wz-fs-42, wz-fw-400')}>
                    <View className={cx(styles.expertGoodAt)}>{genContext()}</View>
                </View>
            )}
            {/* 底部去咨询按钮 */}
            {btnChildren}
        </View>
    );
};

DoctorInfoInZH.displayName = 'DoctorInfoInZH';
export default memo(DoctorInfoInZH);
