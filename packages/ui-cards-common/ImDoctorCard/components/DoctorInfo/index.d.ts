import type {ActionInfo} from '@baidu/vita-pages-im/src/typings';
import type {ReactNode} from 'react';
import type {ButtonInfo, FuDanInfo, PortraitTag} from '../../index.d';

export interface DoctorInfoProps {
    expertName?: string;
    expertDepartment?: string;
    expertLevel?: string;
    expertHospital?: string;
    attributeTag?: AttributeTags[];
    expertGoodAt?: ExpertGoodAt[];
    indicatorList?: IndicatorList[];
    freeTag?: string;
    price?: string;
    pricePre?: string;
    btnInfo?: ButtonInfo;
    actionInfo?: ActionInfo;
    showPortraitTag?: PortraitTag[];
    onCardClick?: () => void;
    onBtnClick?: () => void;
    fuDanInfo?: FuDanInfo[];
    btnChildren?: ReactNode;
    hidePriceBtn?: boolean;
}

export interface AttributeTags {
    text: string;
    key: string;
    color: string;
    borderColor: string;
}

export interface ExpertGoodAt {
    type?: string;
    value?: string;
}

export interface IndicatorList {
    text: string;
    value: string;
    highLightColor: string;
}
