import {useState, useCallback, useRef, useEffect} from 'react';
import {ubcCommonViewSend, ubcCommonClkSend} from '../../../pages-im/src/utils/generalFunction/ubc';
import type {Ubc, DataSourceType, UbcExt} from '../index.d';

/**
 * useState Hooks 提供同步获取setState最新值的方法 getState
 * 使用： const [val, setVal, getVal] = useGetState(0);
 * @param initVal 初始值
 * @returns
 */
export function useGetState<T>(initVal: T): [T, (newVal: T) => Promise<T>, () => T] {
    const [state, setState] = useState<T>(initVal);
    const ref = useRef(initVal);
    const setStateCopy = (newVal: T): Promise<T> => {
        return new Promise(resolve => {
            setState(newVal);
            ref.current = newVal;
            resolve(newVal);
        });
    };
    const getState = () => ref.current;

    return [state, setStateCopy, getState];
}

/**
 * 使用setTimeout的自定义Hook
 *
 * @param callback 延迟执行的回调函数
 * @param delay 延迟时间，单位为毫秒
 * @returns 返回一个函数，调用该函数可启动或停止setTimeout
 */
export function useTimeout<T extends(...args: any[]) => any>(callback: T, delay: number) {
    const timeoutId = useRef<ReturnType<typeof setTimeout>>();
    const savedCallback = useRef(callback);
    const isTimeoutActive = useRef(false);
    const isMounted = useRef(true);

    // 保持回调最新
    useEffect(() => {
        savedCallback.current = callback;
        if (isTimeoutActive.current) {
            setisTimeoutActive(true);
        }
    }, [callback]);

    // 追踪组件挂载状态
    useEffect(() => {
        return () => {
            isMounted.current = false;
        };
    }, []);

    const setisTimeoutActive = useCallback(
        (active: boolean) => {
            if (!isMounted.current) return;

            if (timeoutId.current) {
                clearTimeout(timeoutId.current);
            }

            isTimeoutActive.current = active;

            if (active) {
                timeoutId.current = setTimeout(() => {
                    if (isMounted.current) {
                        savedCallback.current();
                    }
                }, delay);
            }
        },
        [delay]
    );

    return setisTimeoutActive;
}

const ubcHandleMap = {
    view: ubcCommonViewSend,
    clk: ubcCommonClkSend
};

/**
 * imFlow Hooks 通用ubc打点能力，携带意图信息
 * 使用方式：
 * const {commonUbcInteraction} = useUbc(ubc);
 * commonUbcInteraction({type = 'view', value = 'imFlow', ext = {}})
 * @param ubc 初始值，接口返回
 * @returns
 */

export function useUbc({ubc, dataSource}: {ubc?: Ubc; dataSource?: DataSourceType}) {
    const ubcRef = useRef(ubc);
    const dataSourceRef = useRef(dataSource);

    useEffect(() => {
        if (ubc?.value) {
            ubcRef.current = ubc;
        }
    }, [ubc]);

    useEffect(() => {
        if (dataSource) {
            dataSourceRef.current = dataSource;
        }
    }, [dataSource]);

    const commonUbcInteraction = useCallback(
        ({
            type = 'view',
            value = ubcRef?.current?.value,
            ext = {},
            triggerType = ['conversation']
        }: {
            type?: 'view' | 'clk';
            value?: string;
            ext?: UbcExt;
            triggerType?: DataSourceType[];
        }) => {
            if (!triggerType.includes(dataSourceRef.current as DataSourceType)) return;
            if (!value) return;

            ubcHandleMap?.[type]?.({
                value,
                ext: {
                    product_info: {
                        ...(ubcRef?.current?.productionInfo || {})
                    },
                    ...ext
                }
            });
        },
        [ubcRef, dataSourceRef]
    );

    return {commonUbcInteraction};
}
