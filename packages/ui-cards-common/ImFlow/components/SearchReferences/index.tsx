import React, {type FC, useState, useCallback, useMemo} from 'react';
import {View, Text} from '@tarojs/components';
import {WiseDownArrow, WiseUpArrow} from '@baidu/wz-taro-tools-icons';
import cx from 'classnames';

import MarkDown from '../MarkDown';
import type {SearchReferencesProps} from './index.d';

import styles from './index.module.less';
const SearchReferences: FC<SearchReferencesProps> = ({searchReferences, msgEnd}) => {
    // 是否展开思考链
    const [isOpen, setIsOpen] = useState(true);

    const handleOpen = useCallback(() => {
        setIsOpen(!isOpen);
    }, [isOpen]);

    const memoShowIcon = useMemo(() => {
        if (searchReferences?.isFinish || msgEnd) {
            return isOpen ? (
                <WiseDownArrow onClick={handleOpen} />
            ) : (
                <WiseUpArrow onClick={handleOpen} />
            );
        }
        return null;
    }, [searchReferences?.isFinish, msgEnd, isOpen, handleOpen]);

    return (
        <View className={cx(styles.searchReferences, 'wz-plr-18 wz-mt-36')}>
            <View className={styles.searchReferencesTitle}>
                <Text className={cx('searchReferencesTitleText', 'wz-fs-48')}>
                    {searchReferences?.title}
                </Text>
                {memoShowIcon}
            </View>
            {isOpen ? (
                <MarkDown
                    content={searchReferences?.content || ''}
                    isFinish={searchReferences?.isFinish}
                    isShowLoading={false}
                    msgEnd={msgEnd}
                    isTypewriter
                    className={cx('wz-fs-48', styles.markDownContent)}
                />
            ) : null}
        </View>
    );
};
SearchReferences.displayName = 'SearchReferences';
export default SearchReferences;
